import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { Database } from '@/types/database'

// Server-side Supabase client
export const createServerSupabase = () =>
  createServerComponentClient<Database>({ cookies })

// Helper function to get user profile with tenant info
export async function getUserProfile(userId: string) {
  const supabase = createServerSupabase()
  
  const { data: user, error } = await supabase
    .from('users')
    .select(`
      *,
      tenant:tenants(*)
    `)
    .eq('id', userId)
    .single()
    
  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
  
  return user
}

// Helper function to check user permissions
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'provider': 5,
    'reseller': 4,
    'admin': 3,
    'support': 2,
    'sales': 2,
    'staff': 1,
    'client': 0
  }
  
  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0
  
  return userLevel >= requiredLevel
}

// Helper function to get tenant-specific data
export async function getTenantData(tenantId: string) {
  const supabase = createServerSupabase()
  
  const { data: tenant, error } = await supabase
    .from('tenants')
    .select('*')
    .eq('id', tenantId)
    .single()
    
  if (error) {
    console.error('Error fetching tenant data:', error)
    return null
  }
  
  return tenant
}
