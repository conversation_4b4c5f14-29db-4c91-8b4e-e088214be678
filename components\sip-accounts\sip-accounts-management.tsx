'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createClientSupabase } from '@/lib/supabase'
import { formatDate, getStatusColor, generateSIPUsername, generatePassword } from '@/lib/utils'
import { 
  Plus, 
  Search, 
  Phone, 
  User, 
  Key, 
  Globe,
  Copy,
  Edit,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react'
import { SipAccountDialog } from './sip-account-dialog'
import { LivePreview } from '@/components/live-preview/live-preview'
import toast from 'react-hot-toast'

interface SipAccount {
  id: string
  username: string
  password: string
  domain: string
  display_name: string | null
  status: 'active' | 'suspended' | 'inactive'
  settings: any
  created_at: string
  client: {
    id: string
    name: string
    email: string | null
  }
}

interface SipAccountsManagementProps {
  profile: any
}

export function SipAccountsManagement({ profile }: SipAccountsManagementProps) {
  const [sipAccounts, setSipAccounts] = useState<SipAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedAccount, setSelectedAccount] = useState<SipAccount | null>(null)
  const [showAccountDialog, setShowAccountDialog] = useState(false)
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({})
  const [previewAccount, setPreviewAccount] = useState<SipAccount | null>(null)

  const supabase = createClientSupabase()

  useEffect(() => {
    fetchSipAccounts()
  }, [])

  const fetchSipAccounts = async () => {
    try {
      setLoading(true)

      // Check if we're in demo mode
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - return mock data
        const demoSipAccounts = [
          {
            id: 'demo-sip-1',
            username: 'acme_user1',
            password: 'SecurePass123!',
            domain: 'sip.yourplatform.com',
            display_name: 'John Doe',
            status: 'active' as const,
            settings: {
              codec: 'G.711',
              dtmf_mode: 'rfc2833',
              nat: true,
              encryption: false,
              transport: 'UDP'
            },
            created_at: new Date(Date.now() - ******** * 5).toISOString(),
            client: {
              id: 'demo-client-1',
              name: 'Acme Corporation',
              email: '<EMAIL>'
            }
          },
          {
            id: 'demo-sip-2',
            username: 'techstart_admin',
            password: 'MySecretPass456#',
            domain: 'sip.yourplatform.com',
            display_name: 'Jane Smith',
            status: 'active' as const,
            settings: {
              codec: 'G.729',
              dtmf_mode: 'rfc2833',
              nat: true,
              encryption: true,
              transport: 'TLS'
            },
            created_at: new Date(Date.now() - ******** * 2).toISOString(),
            client: {
              id: 'demo-client-2',
              name: 'TechStart Inc',
              email: '<EMAIL>'
            }
          },
          {
            id: 'demo-sip-3',
            username: 'global_support',
            password: 'TempPass789$',
            domain: 'sip.yourplatform.com',
            display_name: 'Support Team',
            status: 'suspended' as const,
            settings: {
              codec: 'G.711',
              dtmf_mode: 'inband',
              nat: false,
              encryption: false,
              transport: 'UDP'
            },
            created_at: new Date(Date.now() - ******** * 10).toISOString(),
            client: {
              id: 'demo-client-3',
              name: 'Global Solutions',
              email: '<EMAIL>'
            }
          }
        ]

        setSipAccounts(demoSipAccounts)
        setLoading(false)
        return
      }

      // Real Supabase mode
      let query = supabase
        .from('sip_accounts')
        .select(`
          *,
          client:clients(id, name, email)
        `)
        .order('created_at', { ascending: false })

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter)
      }

      // If user is a client, only show their accounts
      if (profile.role === 'client') {
        // Find client record for this user
        const { data: clientData } = await supabase
          .from('clients')
          .select('id')
          .eq('email', profile.email)
          .single()

        if (clientData) {
          query = query.eq('client_id', clientData.id)
        }
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching SIP accounts:', error)
        return
      }

      setSipAccounts(data || [])
    } catch (error) {
      console.error('Error fetching SIP accounts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAccount = () => {
    setSelectedAccount(null)
    setShowAccountDialog(true)
  }

  const handleEditAccount = (account: SipAccount) => {
    setSelectedAccount(account)
    setShowAccountDialog(true)
  }

  const handleAccountSaved = () => {
    fetchSipAccounts()
    setShowAccountDialog(false)
    setSelectedAccount(null)
  }

  const togglePasswordVisibility = (accountId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }))
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success(`${label} copied to clipboard!`)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  const handlePreview = (account: SipAccount) => {
    setPreviewAccount(account)
  }

  const filteredAccounts = sipAccounts.filter(account => {
    const matchesSearch = 
      account.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.client.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  const stats = {
    total: sipAccounts.length,
    active: sipAccounts.filter(a => a.status === 'active').length,
    suspended: sipAccounts.filter(a => a.status === 'suspended').length,
    inactive: sipAccounts.filter(a => a.status === 'inactive').length
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">SIP Accounts</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">SIP Accounts</h1>
          <p className="text-gray-600 mt-1">Manage SIP account configurations</p>
        </div>
        {['provider', 'reseller', 'admin'].includes(profile.role) && (
          <Button onClick={handleCreateAccount}>
            <Plus className="h-4 w-4 mr-2" />
            Add SIP Account
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Accounts</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Phone className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-green-600"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.suspended}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-yellow-600"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive</p>
                <p className="text-2xl font-bold text-gray-600">{stats.inactive}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-gray-600"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search SIP accounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SIP Accounts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Accounts List */}
        <div className="space-y-4">
          {filteredAccounts.map((account) => (
            <Card key={account.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      {account.username}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {account.display_name || 'No display name'}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(account.status)}>
                    {account.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Domain:</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm">{account.domain}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(account.domain, 'Domain')}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Password:</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm">
                          {showPasswords[account.id] ? account.password : '••••••••'}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => togglePasswordVisibility(account.id)}
                        >
                          {showPasswords[account.id] ? (
                            <EyeOff className="h-3 w-3" />
                          ) : (
                            <Eye className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(account.password, 'Password')}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Client:</span>
                      <span className="text-sm font-medium">{account.client.name}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t">
                    <span className="text-xs text-gray-500">
                      Created {formatDate(account.created_at)}
                    </span>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreview(account)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {['provider', 'reseller', 'admin'].includes(profile.role) && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAccount(account)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredAccounts.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <Phone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No SIP accounts found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first SIP account'}
                </p>
                {!searchTerm && ['provider', 'reseller', 'admin'].includes(profile.role) && (
                  <Button onClick={handleCreateAccount}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add SIP Account
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Live Preview */}
        <div className="lg:sticky lg:top-6">
          {previewAccount ? (
            <LivePreview
              type="sip-config"
              data={previewAccount}
              onPreview={(data) => console.log('Preview data:', data)}
            />
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">SIP Configuration Preview</h3>
                <p className="text-gray-600">
                  Click the preview button on any SIP account to see its configuration details
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Dialogs */}
      <SipAccountDialog
        open={showAccountDialog}
        onOpenChange={setShowAccountDialog}
        account={selectedAccount}
        onSaved={handleAccountSaved}
      />
    </div>
  )
}
