# Supabase Configuration (Demo values - replace with your actual values)
NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo_anon_key
SUPABASE_SERVICE_ROLE_KEY=demo_service_role_key
NEXT_PUBLIC_SUPABASE_PROJECT_ID=demo_project_id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=SPaaS Platform
NEXT_PUBLIC_APP_DESCRIPTION=SIP Platform as a Service

# SignalWire API (for SIP/DID management) - Demo values
SIGNALWIRE_PROJECT_ID=demo_project_id
SIGNALWIRE_API_TOKEN=demo_api_token
SIGNALWIRE_SPACE_URL=demo.signalwire.com

# Stripe (for billing) - Demo values
STRIPE_PUBLISHABLE_KEY=pk_test_demo
STRIPE_SECRET_KEY=sk_test_demo
STRIPE_WEBHOOK_SECRET=whsec_demo

# Email Service (for notifications) - Demo values
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=demo_app_password

# SMS Service (optional) - Demo values
TWILIO_ACCOUNT_SID=demo_account_sid
TWILIO_AUTH_TOKEN=demo_auth_token
TWILIO_PHONE_NUMBER=+***********

# JWT Secret (for additional security)
JWT_SECRET=demo_jwt_secret_key_change_in_production

# Development
NODE_ENV=development
