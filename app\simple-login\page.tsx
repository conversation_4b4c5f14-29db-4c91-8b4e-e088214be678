'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'react-hot-toast'

export default function SimpleLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Simple demo login - just redirect
      toast.success('Вход выполнен успешно! (Demo Mode)')
      
      // Redirect to dashboard
      router.push('/dashboard')
      
    } catch (error) {
      toast.error('Ошибка входа')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gradient mb-2">SPaaS Platform</h1>
          <p className="text-gray-600">SIP Platform as a Service</p>
        </div>

        {/* Demo Mode Banner */}
        <div className="bg-blue-600 text-white px-4 py-2 rounded-lg text-center text-sm">
          🎮 <strong>Demo Mode</strong> - Введите любые данные для входа
        </div>

        {/* Login Form */}
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Вход в систему</CardTitle>
            <CardDescription>
              Введите любой email и пароль для демонстрации
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email адрес
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Пароль
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="password123"
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                    Запомнить меня
                  </label>
                </div>

                <div className="text-sm">
                  <a href="#" className="font-medium text-blue-600 hover:text-blue-500">
                    Забыли пароль?
                  </a>
                </div>
              </div>

              <div>
                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'Вход...' : 'Войти'}
                </Button>
              </div>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Нет аккаунта?</span>
                </div>
              </div>

              <div className="mt-6">
                <Button
                  onClick={() => router.push('/auth/signup')}
                  variant="outline"
                  className="w-full"
                >
                  Создать новый аккаунт
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Access */}
        <div className="text-center space-y-2">
          <p className="text-sm text-gray-600">Быстрый доступ:</p>
          <div className="space-x-2">
            <Button
              onClick={() => router.push('/dashboard')}
              variant="ghost"
              size="sm"
            >
              Dashboard
            </Button>
            <Button
              onClick={() => router.push('/test-login')}
              variant="ghost"
              size="sm"
            >
              Test Page
            </Button>
            <Button
              onClick={() => router.push('/')}
              variant="ghost"
              size="sm"
            >
              Главная
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <h3 className="text-sm font-medium text-gray-800 mb-2">
            Инструкции для демо:
          </h3>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Введите любой email (например: <EMAIL>)</li>
            <li>• Введите любой пароль (например: password123)</li>
            <li>• Нажмите "Войти" для перехода в админ панель</li>
            <li>• Или используйте кнопку "Dashboard" для прямого перехода</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
