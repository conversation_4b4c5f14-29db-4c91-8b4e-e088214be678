'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { createClientSupabase } from '@/lib/supabase'
import { validateEmail, validatePhone } from '@/lib/utils'
import toast from 'react-hot-toast'
import { Loader2 } from 'lucide-react'

interface Client {
  id: string
  name: string
  email: string | null
  phone: string | null
  company: string | null
  status: 'active' | 'suspended' | 'inactive'
  balance: number
  credit_limit: number
  address?: any
  billing_address?: any
}

interface ClientDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  client?: Client | null
  onSaved: () => void
}

export function ClientDialog({ open, onOpenChange, client, onSaved }: ClientDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    status: 'active' as const,
    balance: 0,
    credit_limit: 1000,
    address: {
      street: '',
      city: '',
      state: '',
      zip: '',
      country: 'US'
    },
    billing_address: {
      street: '',
      city: '',
      state: '',
      zip: '',
      country: 'US'
    }
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const supabase = createClientSupabase()

  useEffect(() => {
    if (client) {
      setFormData({
        name: client.name || '',
        email: client.email || '',
        phone: client.phone || '',
        company: client.company || '',
        status: client.status,
        balance: client.balance || 0,
        credit_limit: client.credit_limit || 1000,
        address: client.address || {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'US'
        },
        billing_address: client.billing_address || {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'US'
        }
      })
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        status: 'active',
        balance: 0,
        credit_limit: 1000,
        address: {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'US'
        },
        billing_address: {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'US'
        }
      })
    }
    setErrors({})
  }, [client, open])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (formData.email && !validateEmail(formData.email)) {
      newErrors.email = 'Invalid email address'
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Invalid phone number'
    }

    if (formData.credit_limit < 0) {
      newErrors.credit_limit = 'Credit limit cannot be negative'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const clientData = {
        name: formData.name.trim(),
        email: formData.email.trim() || null,
        phone: formData.phone.trim() || null,
        company: formData.company.trim() || null,
        status: formData.status,
        balance: formData.balance,
        credit_limit: formData.credit_limit,
        address: formData.address,
        billing_address: formData.billing_address
      }

      if (client) {
        // Update existing client
        const { error } = await supabase
          .from('clients')
          .update(clientData)
          .eq('id', client.id)

        if (error) throw error

        toast.success('Client updated successfully!')
      } else {
        // Create new client
        const { error } = await supabase
          .from('clients')
          .insert(clientData)

        if (error) throw error

        toast.success('Client created successfully!')
      }

      onSaved()
    } catch (error: any) {
      console.error('Error saving client:', error)
      toast.error(error.message || 'Failed to save client')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleAddressChange = (type: 'address' | 'billing_address', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value
      }
    }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {client ? 'Edit Client' : 'Add New Client'}
          </DialogTitle>
          <DialogDescription>
            {client ? 'Update client information' : 'Create a new client account'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Client name"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  placeholder="Company name"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="******-0123"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="balance">Balance ($)</Label>
                <Input
                  id="balance"
                  type="number"
                  step="0.01"
                  value={formData.balance}
                  onChange={(e) => handleInputChange('balance', parseFloat(e.target.value) || 0)}
                />
              </div>

              <div>
                <Label htmlFor="credit_limit">Credit Limit ($)</Label>
                <Input
                  id="credit_limit"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.credit_limit}
                  onChange={(e) => handleInputChange('credit_limit', parseFloat(e.target.value) || 0)}
                  className={errors.credit_limit ? 'border-red-500' : ''}
                />
                {errors.credit_limit && (
                  <p className="text-sm text-red-500 mt-1">{errors.credit_limit}</p>
                )}
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Address Information</h3>
            
            <div className="grid grid-cols-2 gap-6">
              {/* Billing Address */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-700">Billing Address</h4>
                <Input
                  placeholder="Street address"
                  value={formData.billing_address.street}
                  onChange={(e) => handleAddressChange('billing_address', 'street', e.target.value)}
                />
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="City"
                    value={formData.billing_address.city}
                    onChange={(e) => handleAddressChange('billing_address', 'city', e.target.value)}
                  />
                  <Input
                    placeholder="State"
                    value={formData.billing_address.state}
                    onChange={(e) => handleAddressChange('billing_address', 'state', e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="ZIP Code"
                    value={formData.billing_address.zip}
                    onChange={(e) => handleAddressChange('billing_address', 'zip', e.target.value)}
                  />
                  <Input
                    placeholder="Country"
                    value={formData.billing_address.country}
                    onChange={(e) => handleAddressChange('billing_address', 'country', e.target.value)}
                  />
                </div>
              </div>

              {/* Service Address */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-700">Service Address</h4>
                <Input
                  placeholder="Street address"
                  value={formData.address.street}
                  onChange={(e) => handleAddressChange('address', 'street', e.target.value)}
                />
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="City"
                    value={formData.address.city}
                    onChange={(e) => handleAddressChange('address', 'city', e.target.value)}
                  />
                  <Input
                    placeholder="State"
                    value={formData.address.state}
                    onChange={(e) => handleAddressChange('address', 'state', e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="ZIP Code"
                    value={formData.address.zip}
                    onChange={(e) => handleAddressChange('address', 'zip', e.target.value)}
                  />
                  <Input
                    placeholder="Country"
                    value={formData.address.country}
                    onChange={(e) => handleAddressChange('address', 'country', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {client ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                client ? 'Update Client' : 'Create Client'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
