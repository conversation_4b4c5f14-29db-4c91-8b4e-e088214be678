# 🚀 SPaaS Platform Deployment Guide

This guide covers deploying the SPaaS Platform to production environments.

## 📋 Prerequisites

- Supabase account and project
- Vercel account (for frontend deployment)
- SignalWire account (for SIP services)
- Stripe account (for billing)
- Domain name (optional, for custom branding)

## 🔧 Environment Setup

### 1. Supabase Configuration

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and keys
3. Run the database migrations:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref YOUR_PROJECT_ID

# Run migrations
supabase db push
```

### 2. Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_SUPABASE_PROJECT_ID=your_project_id

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=SPaaS Platform
NEXT_PUBLIC_APP_DESCRIPTION=SIP Platform as a Service

# SignalWire (for SIP/DID management)
SIGNALWIRE_PROJECT_ID=your_signalwire_project_id
SIGNALWIRE_API_TOKEN=your_signalwire_api_token
SIGNALWIRE_SPACE_URL=your_signalwire_space_url

# Stripe (for billing)
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# JWT Secret
JWT_SECRET=your_jwt_secret_key

# Production
NODE_ENV=production
```

## 🌐 Frontend Deployment (Vercel)

### 1. Connect Repository

1. Go to [vercel.com](https://vercel.com) and sign in
2. Click "New Project" and import your repository
3. Configure the project settings:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`

### 2. Environment Variables

Add all environment variables from your `.env.local` file to Vercel:

1. Go to Project Settings > Environment Variables
2. Add each variable one by one
3. Make sure to set the environment to "Production"

### 3. Deploy

1. Click "Deploy" to start the deployment
2. Vercel will automatically deploy on every push to main branch

## 🔗 Custom Domain Setup

### 1. Add Domain to Vercel

1. Go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS records as instructed

### 2. Update Environment Variables

Update the following variables with your custom domain:

```bash
NEXT_PUBLIC_APP_URL=https://your-custom-domain.com
```

## 🔌 Webhook Configuration

### 1. Supabase Edge Functions

Deploy the webhook functions to Supabase:

```bash
# Deploy SignalWire webhook
supabase functions deploy signalwire-webhook

# Deploy Stripe webhook
supabase functions deploy stripe-webhook
```

### 2. SignalWire Webhooks

1. Go to SignalWire Console > Webhooks
2. Add webhook URL: `https://your-project.supabase.co/functions/v1/signalwire-webhook`
3. Select events: Call events, Number events

### 3. Stripe Webhooks

1. Go to Stripe Dashboard > Webhooks
2. Add endpoint: `https://your-project.supabase.co/functions/v1/stripe-webhook`
3. Select events:
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

## 🔒 Security Configuration

### 1. Supabase RLS Policies

Ensure all RLS policies are enabled and properly configured:

```sql
-- Check RLS is enabled on all tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

### 2. API Rate Limiting

Configure rate limiting in Supabase:

1. Go to Settings > API
2. Set appropriate rate limits for your use case

### 3. CORS Configuration

Update CORS settings in Supabase:

1. Go to Settings > API
2. Add your domain to allowed origins

## 📊 Monitoring & Analytics

### 1. Supabase Monitoring

- Monitor database performance in Supabase Dashboard
- Set up alerts for high CPU/memory usage
- Monitor API usage and rate limits

### 2. Vercel Analytics

- Enable Vercel Analytics for performance monitoring
- Monitor Core Web Vitals and user experience

### 3. Error Tracking

Consider integrating error tracking services:

- Sentry for error monitoring
- LogRocket for session replay
- Datadog for comprehensive monitoring

## 🔄 CI/CD Pipeline

### 1. GitHub Actions (Optional)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm run test
```

### 2. Database Migrations

Set up automatic migrations:

```bash
# Add to your CI/CD pipeline
supabase db push --linked
```

## 🧪 Testing in Production

### 1. Health Checks

Create health check endpoints:

- `/api/health` - Basic health check
- `/api/health/database` - Database connectivity
- `/api/health/external` - External service connectivity

### 2. Smoke Tests

Run basic smoke tests after deployment:

- User registration/login
- Basic CRUD operations
- Payment processing (test mode)
- Webhook delivery

## 📈 Scaling Considerations

### 1. Database Scaling

- Monitor connection pool usage
- Consider read replicas for heavy read workloads
- Implement database connection pooling

### 2. CDN Configuration

- Use Vercel's built-in CDN
- Configure caching headers appropriately
- Optimize images and static assets

### 3. Performance Optimization

- Enable Next.js Image Optimization
- Implement proper caching strategies
- Monitor and optimize Core Web Vitals

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check environment variables
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Database Connection Issues**
   - Verify Supabase credentials
   - Check RLS policies
   - Monitor connection limits

3. **Webhook Failures**
   - Check webhook URLs
   - Verify authentication
   - Monitor function logs

### Support Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)

## 📝 Post-Deployment Checklist

- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] Webhooks configured and tested
- [ ] Custom domain configured (if applicable)
- [ ] SSL certificate active
- [ ] Monitoring and alerts set up
- [ ] Backup strategy implemented
- [ ] Security audit completed
- [ ] Performance testing completed
- [ ] Documentation updated

---

🎉 **Congratulations!** Your SPaaS Platform is now deployed and ready for production use.
