'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { createClientSupabase } from '@/lib/supabase'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Users,
  Phone,
  CreditCard,
  Ticket,
  TrendingUp,
  TrendingDown,
  PhoneCall,
  DollarSign,
  Activity,
  AlertCircle
} from 'lucide-react'

interface DashboardOverviewProps {
  profile: any
}

interface DashboardStats {
  totalClients: number
  activeSipAccounts: number
  totalDidNumbers: number
  openTickets: number
  monthlyRevenue: number
  recentActivity: any[]
}

export function DashboardOverview({ profile }: DashboardOverviewProps) {
  const [stats, setStats] = useState<DashboardStats>({
    totalClients: 0,
    activeSipAccounts: 0,
    totalDidNumbers: 0,
    openTickets: 0,
    monthlyRevenue: 0,
    recentActivity: []
  })
  const [loading, setLoading] = useState(true)
  const supabase = createClientSupabase()

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      // Check if we're in demo mode
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - return mock data
        setStats({
          totalClients: 12,
          activeSipAccounts: 28,
          totalDidNumbers: 15,
          openTickets: 3,
          monthlyRevenue: 2450.00,
          recentActivity: [
            {
              id: '1',
              action: 'client_created',
              resource_type: 'client',
              resource_id: 'demo-1',
              created_at: new Date().toISOString(),
              details: { client_name: 'Demo Client' }
            },
            {
              id: '2',
              action: 'sip_account_created',
              resource_type: 'sip_account',
              resource_id: 'demo-2',
              created_at: new Date(Date.now() - 3600000).toISOString(),
              details: { username: 'demo_user' }
            },
            {
              id: '3',
              action: 'call_flow_updated',
              resource_type: 'call_flow',
              resource_id: 'demo-3',
              created_at: new Date(Date.now() - 7200000).toISOString(),
              details: { flow_name: 'Customer Support' }
            }
          ]
        })
        setLoading(false)
        return
      }

      // Real Supabase mode
      const [
        clientsResult,
        sipAccountsResult,
        didNumbersResult,
        ticketsResult,
        invoicesResult,
        logsResult
      ] = await Promise.all([
        supabase.from('clients').select('id', { count: 'exact' }),
        supabase.from('sip_accounts').select('id', { count: 'exact' }).eq('status', 'active'),
        supabase.from('did_numbers').select('id', { count: 'exact' }),
        supabase.from('tickets').select('id', { count: 'exact' }).eq('status', 'open'),
        supabase.from('invoices').select('total_amount').eq('status', 'paid').gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),
        supabase.from('logs').select('*').order('created_at', { ascending: false }).limit(5)
      ])

      const monthlyRevenue = invoicesResult.data?.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0) || 0

      setStats({
        totalClients: clientsResult.count || 0,
        activeSipAccounts: sipAccountsResult.count || 0,
        totalDidNumbers: didNumbersResult.count || 0,
        openTickets: ticketsResult.count || 0,
        monthlyRevenue,
        recentActivity: logsResult.data || []
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Clients',
      value: stats.totalClients,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'increase'
    },
    {
      title: 'Active SIP Accounts',
      value: stats.activeSipAccounts,
      icon: Phone,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+8%',
      changeType: 'increase'
    },
    {
      title: 'DID Numbers',
      value: stats.totalDidNumbers,
      icon: PhoneCall,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+5%',
      changeType: 'increase'
    },
    {
      title: 'Open Tickets',
      value: stats.openTickets,
      icon: Ticket,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: '-3%',
      changeType: 'decrease'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(stats.monthlyRevenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      change: '+15%',
      changeType: 'increase'
    }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {profile.first_name}!
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your {profile.tenant?.name} platform today.
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Tenant</p>
            <p className="font-semibold text-gray-900">{profile.tenant?.name}</p>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {profile.tenant?.status}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {typeof stat.value === 'string' ? stat.value : stat.value.toLocaleString()}
                  </p>
                  <div className="flex items-center mt-2">
                    {stat.changeType === 'increase' ? (
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last month</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest actions in your platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recentActivity.length > 0 ? (
                stats.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <Activity className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.action}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatDate(activity.created_at)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent activity</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to get you started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Add New Client
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Phone className="h-4 w-4 mr-2" />
                Create SIP Account
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <PhoneCall className="h-4 w-4 mr-2" />
                Purchase DID Number
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Ticket className="h-4 w-4 mr-2" />
                Create Support Ticket
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm font-medium text-gray-900">SIP Service</span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm font-medium text-gray-900">Billing System</span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm font-medium text-gray-900">API Services</span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Operational
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
