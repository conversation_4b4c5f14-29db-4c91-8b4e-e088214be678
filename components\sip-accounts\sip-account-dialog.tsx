'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { createClientSupabase } from '@/lib/supabase'
import { generateSIPUsername, generatePassword } from '@/lib/utils'
import toast from 'react-hot-toast'
import { Loader2, RefreshCw, Eye, EyeOff } from 'lucide-react'

interface SipAccount {
  id: string
  username: string
  password: string
  domain: string
  display_name: string | null
  status: 'active' | 'suspended' | 'inactive'
  settings: any
  client_id: string
}

interface Client {
  id: string
  name: string
  email: string | null
}

interface SipAccountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  account?: SipAccount | null
  onSaved: () => void
}

export function SipAccountDialog({ open, onOpenChange, account, onSaved }: SipAccountDialogProps) {
  const [loading, setLoading] = useState(false)
  const [clients, setClients] = useState<Client[]>([])
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    client_id: '',
    username: '',
    password: '',
    domain: 'sip.yourplatform.com',
    display_name: '',
    status: 'active' as const,
    settings: {
      codec: 'G.711',
      dtmf_mode: 'rfc2833',
      nat: true,
      encryption: false,
      transport: 'UDP'
    }
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const supabase = createClientSupabase()

  useEffect(() => {
    if (open) {
      fetchClients()
    }
  }, [open])

  useEffect(() => {
    if (account) {
      setFormData({
        client_id: account.client_id,
        username: account.username,
        password: account.password,
        domain: account.domain,
        display_name: account.display_name || '',
        status: account.status,
        settings: account.settings || {
          codec: 'G.711',
          dtmf_mode: 'rfc2833',
          nat: true,
          encryption: false,
          transport: 'UDP'
        }
      })
    } else {
      setFormData({
        client_id: '',
        username: generateSIPUsername(),
        password: generatePassword(16),
        domain: 'sip.yourplatform.com',
        display_name: '',
        status: 'active',
        settings: {
          codec: 'G.711',
          dtmf_mode: 'rfc2833',
          nat: true,
          encryption: false,
          transport: 'UDP'
        }
      })
    }
    setErrors({})
  }, [account, open])

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name, email')
        .eq('status', 'active')
        .order('name')

      if (error) throw error

      setClients(data || [])
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.client_id) {
      newErrors.client_id = 'Client is required'
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters'
    }

    if (!formData.domain.trim()) {
      newErrors.domain = 'Domain is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const accountData = {
        client_id: formData.client_id,
        username: formData.username.trim(),
        password: formData.password.trim(),
        domain: formData.domain.trim(),
        display_name: formData.display_name.trim() || null,
        status: formData.status,
        settings: formData.settings
      }

      if (account) {
        // Update existing account
        const { error } = await supabase
          .from('sip_accounts')
          .update(accountData)
          .eq('id', account.id)

        if (error) throw error

        toast.success('SIP account updated successfully!')
      } else {
        // Create new account
        const { error } = await supabase
          .from('sip_accounts')
          .insert(accountData)

        if (error) throw error

        toast.success('SIP account created successfully!')
      }

      onSaved()
    } catch (error: any) {
      console.error('Error saving SIP account:', error)
      if (error.code === '23505') {
        toast.error('Username already exists. Please choose a different username.')
      } else {
        toast.error(error.message || 'Failed to save SIP account')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleSettingsChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }))
  }

  const generateNewCredentials = () => {
    setFormData(prev => ({
      ...prev,
      username: generateSIPUsername(),
      password: generatePassword(16)
    }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {account ? 'Edit SIP Account' : 'Add New SIP Account'}
          </DialogTitle>
          <DialogDescription>
            {account ? 'Update SIP account configuration' : 'Create a new SIP account for a client'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div>
              <Label htmlFor="client_id">Client *</Label>
              <Select
                value={formData.client_id}
                onValueChange={(value) => handleInputChange('client_id', value)}
              >
                <SelectTrigger className={errors.client_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name} {client.email && `(${client.email})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.client_id && (
                <p className="text-sm text-red-500 mt-1">{errors.client_id}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="username">Username *</Label>
                <div className="flex space-x-2">
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder="SIP username"
                    className={errors.username ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateNewCredentials}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
                {errors.username && (
                  <p className="text-sm text-red-500 mt-1">{errors.username}</p>
                )}
              </div>

              <div>
                <Label htmlFor="display_name">Display Name</Label>
                <Input
                  id="display_name"
                  value={formData.display_name}
                  onChange={(e) => handleInputChange('display_name', e.target.value)}
                  placeholder="John Doe"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="password">Password *</Label>
                <div className="flex space-x-2">
                  <div className="relative flex-1">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="SIP password"
                      className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500 mt-1">{errors.password}</p>
                )}
              </div>

              <div>
                <Label htmlFor="domain">Domain *</Label>
                <Input
                  id="domain"
                  value={formData.domain}
                  onChange={(e) => handleInputChange('domain', e.target.value)}
                  placeholder="sip.yourplatform.com"
                  className={errors.domain ? 'border-red-500' : ''}
                />
                {errors.domain && (
                  <p className="text-sm text-red-500 mt-1">{errors.domain}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Advanced Settings</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="codec">Codec</Label>
                <Select
                  value={formData.settings.codec}
                  onValueChange={(value) => handleSettingsChange('codec', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="G.711">G.711 (PCMU/PCMA)</SelectItem>
                    <SelectItem value="G.729">G.729</SelectItem>
                    <SelectItem value="G.722">G.722</SelectItem>
                    <SelectItem value="Opus">Opus</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="transport">Transport</Label>
                <Select
                  value={formData.settings.transport}
                  onValueChange={(value) => handleSettingsChange('transport', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UDP">UDP</SelectItem>
                    <SelectItem value="TCP">TCP</SelectItem>
                    <SelectItem value="TLS">TLS</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dtmf_mode">DTMF Mode</Label>
                <Select
                  value={formData.settings.dtmf_mode}
                  onValueChange={(value) => handleSettingsChange('dtmf_mode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rfc2833">RFC2833</SelectItem>
                    <SelectItem value="inband">Inband</SelectItem>
                    <SelectItem value="info">SIP INFO</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-4 pt-6">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.settings.nat}
                    onChange={(e) => handleSettingsChange('nat', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">NAT Support</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.settings.encryption}
                    onChange={(e) => handleSettingsChange('encryption', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Encryption</span>
                </label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {account ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                account ? 'Update Account' : 'Create Account'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
