'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createClientSupabase } from '@/lib/supabase'
import { formatDate } from '@/lib/utils'
import { 
  Plus, 
  Search, 
  Workflow, 
  Play, 
  Edit, 
  Trash2, 
  Co<PERSON>,
  Eye,
  Settings
} from 'lucide-react'
import { CallFlowBuilder } from './call-flow-builder'
import { LivePreview } from '@/components/live-preview/live-preview'
import toast from 'react-hot-toast'

interface CallFlow {
  id: string
  name: string
  description: string | null
  flow_data: any
  is_active: boolean
  created_at: string
  updated_at: string
}

interface CallFlowsManagementProps {
  profile: any
}

export function CallFlowsManagement({ profile }: CallFlowsManagementProps) {
  const [callFlows, setCallFlows] = useState<CallFlow[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFlow, setSelectedFlow] = useState<CallFlow | null>(null)
  const [showBuilder, setShowBuilder] = useState(false)
  const [previewFlow, setPreviewFlow] = useState<CallFlow | null>(null)

  const supabase = createClientSupabase()

  useEffect(() => {
    fetchCallFlows()
  }, [])

  const fetchCallFlows = async () => {
    try {
      setLoading(true)

      // Check if we're in demo mode
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - return mock data
        const demoCallFlows = [
          {
            id: 'demo-flow-1',
            name: 'Customer Support IVR',
            description: 'Main customer support call flow with menu options',
            is_active: true,
            created_at: new Date(Date.now() - 86400000 * 3).toISOString(),
            updated_at: new Date(Date.now() - 86400000 * 1).toISOString(),
            flow_data: {
              steps: [
                {
                  id: '1',
                  type: 'greeting',
                  name: 'Welcome Message',
                  description: 'Initial greeting for callers',
                  settings: {
                    message: 'Thank you for calling our support center. Your call is important to us.',
                    voice: 'alice',
                    language: 'en-US'
                  }
                },
                {
                  id: '2',
                  type: 'menu',
                  name: 'Main Menu',
                  description: 'Route callers to appropriate department',
                  settings: {
                    message: 'Press 1 for Sales, 2 for Technical Support, 3 for Billing, or 0 for Operator',
                    timeout: 10,
                    retries: 3
                  },
                  routes: {
                    '1': 'sales-queue',
                    '2': 'support-queue',
                    '3': 'billing-queue',
                    '0': 'operator'
                  }
                },
                {
                  id: '3',
                  type: 'queue',
                  name: 'Support Queue',
                  description: 'Hold queue for technical support',
                  settings: {
                    music: 'default',
                    announcement: 'You are in the technical support queue. Please hold.',
                    announcement_interval: 30
                  }
                }
              ]
            }
          },
          {
            id: 'demo-flow-2',
            name: 'Sales Department',
            description: 'Direct sales line with business hours check',
            is_active: true,
            created_at: new Date(Date.now() - 86400000 * 7).toISOString(),
            updated_at: new Date(Date.now() - 86400000 * 2).toISOString(),
            flow_data: {
              steps: [
                {
                  id: '1',
                  type: 'greeting',
                  name: 'Sales Greeting',
                  settings: {
                    message: 'Welcome to our sales department. We are here to help you find the perfect solution.',
                    voice: 'bob',
                    language: 'en-US'
                  }
                },
                {
                  id: '2',
                  type: 'transfer',
                  name: 'Transfer to Sales',
                  settings: {
                    destination: '+1-555-SALES',
                    message: 'Connecting you to our sales team now.'
                  }
                }
              ]
            }
          },
          {
            id: 'demo-flow-3',
            name: 'After Hours Message',
            description: 'Message for calls outside business hours',
            is_active: false,
            created_at: new Date(Date.now() - 86400000 * 14).toISOString(),
            updated_at: new Date(Date.now() - 86400000 * 5).toISOString(),
            flow_data: {
              steps: [
                {
                  id: '1',
                  type: 'greeting',
                  name: 'After Hours Message',
                  settings: {
                    message: 'Thank you for calling. Our office hours are Monday through Friday, 9 AM to 5 PM. Please call back during business hours or leave a message.',
                    voice: 'alice',
                    language: 'en-US'
                  }
                },
                {
                  id: '2',
                  type: 'record',
                  name: 'Voice Message',
                  settings: {
                    message: 'Please leave your message after the tone.',
                    max_duration: 120,
                    beep: true
                  }
                },
                {
                  id: '3',
                  type: 'hangup',
                  name: 'End Call',
                  settings: {
                    message: 'Thank you for your message. Goodbye.'
                  }
                }
              ]
            }
          }
        ]

        setCallFlows(demoCallFlows)
        setLoading(false)
        return
      }

      // Real Supabase mode
      const { data, error } = await supabase
        .from('call_flows')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching call flows:', error)
        return
      }

      setCallFlows(data || [])
    } catch (error) {
      console.error('Error fetching call flows:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateFlow = () => {
    setSelectedFlow(null)
    setShowBuilder(true)
  }

  const handleEditFlow = (flow: CallFlow) => {
    setSelectedFlow(flow)
    setShowBuilder(true)
  }

  const handleFlowSaved = () => {
    fetchCallFlows()
    setShowBuilder(false)
    setSelectedFlow(null)
  }

  const handlePreview = (flow: CallFlow) => {
    setPreviewFlow(flow)
  }

  const handleDuplicate = async (flow: CallFlow) => {
    try {
      const duplicatedFlow = {
        name: `${flow.name} (Copy)`,
        description: flow.description,
        flow_data: flow.flow_data,
        is_active: false
      }

      const { error } = await supabase
        .from('call_flows')
        .insert(duplicatedFlow)

      if (error) throw error

      toast.success('Call flow duplicated successfully!')
      fetchCallFlows()
    } catch (error: any) {
      console.error('Error duplicating call flow:', error)
      toast.error(error.message || 'Failed to duplicate call flow')
    }
  }

  const handleToggleActive = async (flow: CallFlow) => {
    try {
      const { error } = await supabase
        .from('call_flows')
        .update({ is_active: !flow.is_active })
        .eq('id', flow.id)

      if (error) throw error

      toast.success(`Call flow ${!flow.is_active ? 'activated' : 'deactivated'} successfully!`)
      fetchCallFlows()
    } catch (error: any) {
      console.error('Error updating call flow:', error)
      toast.error(error.message || 'Failed to update call flow')
    }
  }

  const handleDelete = async (flow: CallFlow) => {
    if (!confirm('Are you sure you want to delete this call flow? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('call_flows')
        .delete()
        .eq('id', flow.id)

      if (error) throw error

      toast.success('Call flow deleted successfully!')
      fetchCallFlows()
      
      // Clear preview if deleted flow was being previewed
      if (previewFlow?.id === flow.id) {
        setPreviewFlow(null)
      }
    } catch (error: any) {
      console.error('Error deleting call flow:', error)
      toast.error(error.message || 'Failed to delete call flow')
    }
  }

  const filteredFlows = callFlows.filter(flow => {
    const matchesSearch = 
      flow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      flow.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  const stats = {
    total: callFlows.length,
    active: callFlows.filter(f => f.is_active).length,
    inactive: callFlows.filter(f => !f.is_active).length
  }

  if (showBuilder) {
    return (
      <CallFlowBuilder
        flow={selectedFlow}
        onSaved={handleFlowSaved}
        onCancel={() => setShowBuilder(false)}
      />
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Call Flows</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Call Flows</h1>
          <p className="text-gray-600 mt-1">Design and manage IVR call flows</p>
        </div>
        <Button onClick={handleCreateFlow}>
          <Plus className="h-4 w-4 mr-2" />
          Create Call Flow
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Flows</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Workflow className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-green-600"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive</p>
                <p className="text-2xl font-bold text-gray-600">{stats.inactive}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-gray-600"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search call flows..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Call Flows Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Flows List */}
        <div className="space-y-4">
          {filteredFlows.map((flow) => (
            <Card key={flow.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <Workflow className="h-4 w-4 mr-2" />
                      {flow.name}
                    </CardTitle>
                    {flow.description && (
                      <CardDescription className="mt-1">
                        {flow.description}
                      </CardDescription>
                    )}
                  </div>
                  <Badge variant={flow.is_active ? 'default' : 'secondary'}>
                    {flow.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Steps:</span>
                    <span className="font-medium">
                      {flow.flow_data?.steps?.length || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Last Updated:</span>
                    <span className="font-medium">
                      {formatDate(flow.updated_at)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t">
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreview(flow)}
                        title="Preview"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditFlow(flow)}
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDuplicate(flow)}
                        title="Duplicate"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(flow)}
                        className={flow.is_active ? 'text-yellow-600' : 'text-green-600'}
                        title={flow.is_active ? 'Deactivate' : 'Activate'}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(flow)}
                        className="text-red-600 hover:text-red-700"
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredFlows.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No call flows found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first call flow'}
                </p>
                {!searchTerm && (
                  <Button onClick={handleCreateFlow}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Call Flow
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Live Preview */}
        <div className="lg:sticky lg:top-6">
          {previewFlow ? (
            <LivePreview
              type="call-flow"
              data={previewFlow.flow_data}
              onPreview={(data) => console.log('Preview data:', data)}
              onSave={() => {
                // Handle save from preview if needed
                handleEditFlow(previewFlow)
              }}
            />
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Play className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Call Flow Preview</h3>
                <p className="text-gray-600">
                  Click the preview button on any call flow to see it in action
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
