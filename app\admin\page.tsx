'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Phone, 
  DollarSign, 
  Activity,
  Settings,
  BarChart3,
  Workflow,
  CreditCard,
  Menu,
  X
} from 'lucide-react'

export default function AdminPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const stats = [
    {
      title: 'Total Clients',
      value: '12',
      change: '+2 this month',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Active SIP Accounts',
      value: '28',
      change: '+5 this week',
      icon: Phone,
      color: 'text-green-600'
    },
    {
      title: 'Monthly Revenue',
      value: '$2,450',
      change: '+12% from last month',
      icon: DollarSign,
      color: 'text-yellow-600'
    },
    {
      title: 'System Status',
      value: 'Online',
      change: '99.9% uptime',
      icon: Activity,
      color: 'text-green-600'
    }
  ]

  const clients = [
    { name: 'Acme Corporation', status: 'active', balance: '$150.00', accounts: 3 },
    { name: 'TechStart Inc', status: 'active', balance: '-$25.50', accounts: 1 },
    { name: 'Global Solutions', status: 'suspended', balance: '$0.00', accounts: 5 }
  ]

  const sipAccounts = [
    { username: 'acme_user1', client: 'Acme Corporation', status: 'active', codec: 'G.711' },
    { username: 'techstart_admin', client: 'TechStart Inc', status: 'active', codec: 'G.729' },
    { username: 'global_support', client: 'Global Solutions', status: 'suspended', codec: 'G.711' }
  ]

  const navigation = [
    { name: 'Dashboard', icon: BarChart3, current: true },
    { name: 'Clients', icon: Users, current: false },
    { name: 'SIP Accounts', icon: Phone, current: false },
    { name: 'Call Flows', icon: Workflow, current: false },
    { name: 'Billing', icon: CreditCard, current: false },
    { name: 'Settings', icon: Settings, current: false },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Demo Mode Banner */}
      <div className="bg-blue-600 text-white px-4 py-2 text-center text-sm">
        🎮 <strong>Demo Mode</strong> - Добро пожаловать в админ панель SPaaS Platform!
      </div>

      {/* Mobile sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 flex z-40 md:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-6 w-6 text-white" />
              </button>
            </div>
            <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
              <div className="flex-shrink-0 flex items-center px-4">
                <h1 className="text-xl font-bold text-blue-600">SPaaS Platform</h1>
              </div>
              <nav className="mt-5 px-2 space-y-1">
                {navigation.map((item) => (
                  <a
                    key={item.name}
                    href="#"
                    className={`${
                      item.current
                        ? 'bg-blue-100 text-blue-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
                  >
                    <item.icon className="mr-3 flex-shrink-0 h-5 w-5" />
                    {item.name}
                  </a>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white">
          <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <h1 className="text-xl font-bold text-blue-600">SPaaS Platform</h1>
            </div>
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href="#"
                  className={`${
                    item.current
                      ? 'bg-blue-100 text-blue-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
                >
                  <item.icon className="mr-3 flex-shrink-0 h-5 w-5" />
                  {item.name}
                </a>
              ))}
            </nav>
          </div>
          <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">DU</span>
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">Demo User</p>
                <Badge variant="secondary" className="text-xs">Admin</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="md:pl-64 flex flex-col flex-1">
        <div className="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50">
          <button
            type="button"
            className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <h1 className="text-2xl font-semibold text-gray-900 mb-6">Dashboard</h1>

              {/* Stats */}
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                {stats.map((item) => (
                  <Card key={item.title}>
                    <CardContent className="p-6">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <item.icon className={`h-8 w-8 ${item.color}`} />
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              {item.title}
                            </dt>
                            <dd className="text-lg font-medium text-gray-900">
                              {item.value}
                            </dd>
                            <dd className="text-sm text-gray-500">
                              {item.change}
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Clients Table */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Clients</CardTitle>
                    <CardDescription>Latest client registrations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {clients.map((client, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{client.name}</p>
                            <p className="text-sm text-gray-500">{client.accounts} SIP accounts</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{client.balance}</p>
                            <Badge 
                              variant={client.status === 'active' ? 'default' : 'destructive'}
                              className="text-xs"
                            >
                              {client.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* SIP Accounts Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>SIP Accounts</CardTitle>
                    <CardDescription>Active SIP account status</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {sipAccounts.map((account, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{account.username}</p>
                            <p className="text-sm text-gray-500">{account.client}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{account.codec}</p>
                            <Badge 
                              variant={account.status === 'active' ? 'default' : 'destructive'}
                              className="text-xs"
                            >
                              {account.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common administrative tasks</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Button className="h-20 flex flex-col items-center justify-center">
                      <Users className="h-6 w-6 mb-2" />
                      Add Client
                    </Button>
                    <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                      <Phone className="h-6 w-6 mb-2" />
                      Create SIP Account
                    </Button>
                    <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                      <Workflow className="h-6 w-6 mb-2" />
                      Build Call Flow
                    </Button>
                    <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                      <BarChart3 className="h-6 w-6 mb-2" />
                      View Reports
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
