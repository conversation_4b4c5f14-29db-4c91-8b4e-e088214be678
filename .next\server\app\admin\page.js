/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(rsc)/./app/admin/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cadmin%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cadmin%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(ssr)/./app/admin/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q2FwcCU1Q2FkbWluJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3BhYXMtcGxhdGZvcm0vPzMzZjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIUFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx0ZXN0XFxcXGFwcFxcXFxhZG1pblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cadmin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/auth-provider.tsx */ \"(ssr)/./components/providers/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/theme-provider.tsx */ \"(ssr)/./components/providers/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q2NvbXBvbmVudHMlNUNwcm92aWRlcnMlNUNhdXRoLXByb3ZpZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNjb21wb25lbnRzJTVDcHJvdmlkZXJzJTVDdGhlbWUtcHJvdmlkZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBK0g7QUFDL0gsOExBQWdJO0FBQ2hJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3BhYXMtcGxhdGZvcm0vP2I2ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIUFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx0ZXN0XFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIUFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx0ZXN0XFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXHRoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcdGVzdFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaG90LXRvYXN0XFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,CreditCard,DollarSign,Menu,Phone,Settings,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AdminPage() {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const stats = [\n        {\n            title: \"Total Clients\",\n            value: \"12\",\n            change: \"+2 this month\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Active SIP Accounts\",\n            value: \"28\",\n            change: \"+5 this week\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Monthly Revenue\",\n            value: \"$2,450\",\n            change: \"+12% from last month\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"text-yellow-600\"\n        },\n        {\n            title: \"System Status\",\n            value: \"Online\",\n            change: \"99.9% uptime\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"text-green-600\"\n        }\n    ];\n    const clients = [\n        {\n            name: \"Acme Corporation\",\n            status: \"active\",\n            balance: \"$150.00\",\n            accounts: 3\n        },\n        {\n            name: \"TechStart Inc\",\n            status: \"active\",\n            balance: \"-$25.50\",\n            accounts: 1\n        },\n        {\n            name: \"Global Solutions\",\n            status: \"suspended\",\n            balance: \"$0.00\",\n            accounts: 5\n        }\n    ];\n    const sipAccounts = [\n        {\n            username: \"acme_user1\",\n            client: \"Acme Corporation\",\n            status: \"active\",\n            codec: \"G.711\"\n        },\n        {\n            username: \"techstart_admin\",\n            client: \"TechStart Inc\",\n            status: \"active\",\n            codec: \"G.729\"\n        },\n        {\n            username: \"global_support\",\n            client: \"Global Solutions\",\n            status: \"suspended\",\n            codec: \"G.711\"\n        }\n    ];\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            current: true,\n            href: \"/admin\"\n        },\n        {\n            name: \"Clients\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            current: false,\n            href: \"/dashboard/clients\"\n        },\n        {\n            name: \"SIP Accounts\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            current: false,\n            href: \"/dashboard/sip-accounts\"\n        },\n        {\n            name: \"Call Flows\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            current: false,\n            href: \"/dashboard/call-flows\"\n        },\n        {\n            name: \"Billing\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            current: false,\n            href: \"/dashboard/billing\"\n        },\n        {\n            name: \"Settings\",\n            icon: _barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            current: false,\n            href: \"/dashboard/settings\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-4 py-2 text-center text-sm\",\n                children: [\n                    \"\\uD83C\\uDFAE \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Demo Mode\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 12\n                    }, this),\n                    \" - Добро пожаловать в админ панель SPaaS Platform!\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex z-40 md:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-0 pt-5 pb-4 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center px-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-blue-600\",\n                                            children: \"SPaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 px-2 space-y-1\",\n                                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: `${item.current ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-3 flex-shrink-0 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.name\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center flex-shrink-0 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-blue-600\",\n                                        children: \"SPaaS Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"mt-5 flex-1 px-2 space-y-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: item.href,\n                                            className: `${item.current ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-3 flex-shrink-0 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: \"DU\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Demo User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: \"Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900\",\n                            onClick: ()=>setSidebarOpen(true),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\",\n                                        children: stats.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    className: `h-8 w-8 ${item.color}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-5 w-0 flex-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                            className: \"text-lg font-medium text-gray-900\",\n                                                                            children: item.value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: item.change\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, item.title, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                children: \"Recent Clients\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                children: \"Latest client registrations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: client.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 218,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        client.accounts,\n                                                                                        \" SIP accounts\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 219,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 217,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: client.balance\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 222,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: client.status === \"active\" ? \"default\" : \"destructive\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: client.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 223,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                children: \"SIP Accounts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                children: \"Active SIP account status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: sipAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: account.username\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: account.client\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: account.codec\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 251,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: account.status === \"active\" ? \"default\" : \"destructive\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: account.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        children: \"Quick Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Common administrative tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>window.location.href = \"/dashboard/clients\",\n                                                            className: \"h-20 flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-6 w-6 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add Client\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>window.location.href = \"/dashboard/sip-accounts\",\n                                                            variant: \"outline\",\n                                                            className: \"h-20 flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-6 w-6 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Create SIP Account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>window.location.href = \"/dashboard/call-flows\",\n                                                            variant: \"outline\",\n                                                            className: \"h-20 flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-6 w-6 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Build Call Flow\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>window.location.href = \"/dashboard\",\n                                                            variant: \"outline\",\n                                                            className: \"h-20 flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_CreditCard_DollarSign_Menu_Phone_Settings_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-6 w-6 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View Reports\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if we're in demo mode\n        const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n        if (isDemoMode) {\n            // Demo mode - just set loading to false\n            setLoading(false);\n            return;\n        }\n        // Get initial session\n        const getInitialSession = async ()=>{\n            try {\n                const { data: { session } } = await supabase.auth.getSession();\n                setUser(session?.user ?? null);\n                if (session?.user) {\n                    await fetchUserProfile(session.user.id);\n                }\n            } catch (error) {\n                console.error(\"Error getting session:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(`\n          *,\n          tenant:tenants(*)\n        `).eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            setProfile(data);\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        try {\n            // Check if we're in demo mode (no real Supabase connection)\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - simulate successful login\n                const demoUser = {\n                    id: \"demo-user-id\",\n                    email: email,\n                    user_metadata: {\n                        first_name: \"Demo\",\n                        last_name: \"User\"\n                    }\n                };\n                const demoProfile = {\n                    id: \"demo-user-id\",\n                    tenant_id: \"demo-tenant-id\",\n                    email: email,\n                    first_name: \"Demo\",\n                    last_name: \"User\",\n                    role: \"admin\",\n                    permissions: {},\n                    phone: null,\n                    is_active: true,\n                    tenant: {\n                        id: \"demo-tenant-id\",\n                        name: \"Demo Company\",\n                        domain: \"demo.local\",\n                        logo_url: null,\n                        primary_color: \"#3B82F6\",\n                        secondary_color: \"#1E40AF\",\n                        status: \"active\"\n                    }\n                };\n                setUser(demoUser);\n                setProfile(demoProfile);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed in! (Demo Mode)\");\n                // Force redirect using window.location\n                setTimeout(()=>{\n                    window.location.replace(\"/dashboard\");\n                }, 1000);\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed in!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign in\");\n            throw error;\n        }\n    };\n    const signUp = async (email, password, userData)=>{\n        try {\n            // Check if we're in demo mode\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - simulate successful signup and auto-login\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Account created successfully! (Demo Mode)\");\n                await signIn(email, password);\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: userData\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Check your email for verification link!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign up\");\n            throw error;\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            // Check if we're in demo mode\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - just clear state\n                setUser(null);\n                setProfile(null);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed out! (Demo Mode)\");\n                router.push(\"/\");\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                throw error;\n            }\n            setUser(null);\n            setProfile(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed out!\");\n            router.push(\"/\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign out\");\n            throw error;\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return;\n        try {\n            const { error } = await supabase.from(\"users\").update(updates).eq(\"id\", user.id);\n            if (error) {\n                throw error;\n            }\n            await fetchUserProfile(user.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Profile updated successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to update profile\");\n            throw error;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            await fetchUserProfile(user.id);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQUNtQztBQUcxRCxTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcGFhcy1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzL3RoZW1lLXByb3ZpZGVyLnRzeD8xYmUxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gJ25leHQtdGhlbWVzL2Rpc3QvdHlwZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServiceSupabase: () => (/* binding */ createServiceSupabase),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Client-side Supabase client\nconst createClientSupabase = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Service role client (for admin operations)\nconst createServiceSupabase = ()=>{\n    const supabaseUrl = \"https://demo.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Helper function to check user permissions\nfunction hasPermission(userRole, requiredRole) {\n    const roleHierarchy = {\n        \"provider\": 5,\n        \"reseller\": 4,\n        \"admin\": 3,\n        \"support\": 2,\n        \"sales\": 2,\n        \"staff\": 1,\n        \"client\": 0\n    };\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   generatePassword: () => (/* binding */ generatePassword),\n/* harmony export */   generateSIPUsername: () => (/* binding */ generateSIPUsername),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getRoleColor: () => (/* binding */ getRoleColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction generatePassword(length = 12) {\n    const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\n    let password = \"\";\n    for(let i = 0; i < length; i++){\n        password += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n    return password;\n}\nfunction generateSIPUsername(prefix = \"sip\") {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substring(2, 8);\n    return `${prefix}_${timestamp}_${random}`;\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n    return phoneRegex.test(phone) && phone.replace(/\\D/g, \"\").length >= 10;\n}\nfunction formatPhoneNumber(phone) {\n    const cleaned = phone.replace(/\\D/g, \"\");\n    if (cleaned.length === 10) {\n        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    if (cleaned.length === 11 && cleaned[0] === \"1\") {\n        return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\n    }\n    return phone;\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\nfunction getInitials(firstName, lastName) {\n    const first = firstName?.charAt(0)?.toUpperCase() || \"\";\n    const last = lastName?.charAt(0)?.toUpperCase() || \"\";\n    return first + last || \"??\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"active\":\n            return \"text-green-600 bg-green-100\";\n        case \"suspended\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"inactive\":\n            return \"text-red-600 bg-red-100\";\n        case \"pending\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"open\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"in_progress\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"resolved\":\n            return \"text-green-600 bg-green-100\";\n        case \"closed\":\n            return \"text-gray-600 bg-gray-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getRoleColor(role) {\n    switch(role.toLowerCase()){\n        case \"provider\":\n            return \"text-purple-600 bg-purple-100\";\n        case \"reseller\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"admin\":\n            return \"text-red-600 bg-red-100\";\n        case \"support\":\n            return \"text-green-600 bg-green-100\";\n        case \"sales\":\n            return \"text-orange-600 bg-orange-100\";\n        case \"staff\":\n            return \"text-gray-600 bg-gray-100\";\n        case \"client\":\n            return \"text-indigo-600 bg-indigo-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"807acf88b172\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcGFhcy1wbGF0Zm9ybS8uL2FwcC9nbG9iYWxzLmNzcz8zODI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODA3YWNmODhiMTcyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\app\admin\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./components/providers/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"SPaaS Platform - SIP Platform as a Service\",\n    description: \"Multi-tenant SIP/Telecom platform with billing, support, and real-time management\",\n    keywords: [\n        \"SIP\",\n        \"VoIP\",\n        \"Telecom\",\n        \"Platform\",\n        \"Multi-tenant\",\n        \"Billing\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                },\n                                success: {\n                                    duration: 3000,\n                                    iconTheme: {\n                                        primary: \"#4ade80\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    duration: 5000,\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\theme-provider.tsx#ThemeProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/@radix-ui","vendor-chunks/jose","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();