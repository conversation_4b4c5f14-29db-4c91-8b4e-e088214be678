import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallFlowsManagement } from '@/components/call-flows/call-flows-management'
import { getUserProfile } from '@/lib/supabase-server'

export default async function CallFlowsPage() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const profile = await getUserProfile(session.user.id)
  
  if (!profile) {
    redirect('/auth/signin')
  }

  // Check if user has permission to view call flows
  if (!['provider', 'reseller', 'admin'].includes(profile.role)) {
    redirect('/dashboard')
  }

  return <CallFlowsManagement profile={profile} />
}
