# 🚀 SPaaS Platform - Quick Setup Guide

Добро пожаловать в SPaaS Platform! Этот гайд поможет вам быстро настроить и запустить платформу.

## 📋 Что уже создано

### ✅ Полная архитектура платформы
- **Frontend**: Next.js 14 с TypeScript и Tailwind CSS
- **Backend**: Supabase с PostgreSQL и Row-Level Security
- **Аутентификация**: JWT с ролевой системой доступа
- **UI**: Современные компоненты с Shadcn/ui
- **Live Preview**: Реальное время предварительного просмотра

### ✅ Основные модули
- **Dashboard**: Обзор системы с статистикой
- **Clients Management**: Управление клиентами
- **SIP Accounts**: Управление SIP аккаунтами
- **Call Flow Builder**: Визуальный конструктор IVR
- **Live Preview**: Предварительный просмотр в реальном времени

### ✅ Безопасность и мультитенантность
- Row-Level Security на уровне базы данных
- Роли: Provider, Reseller, Admin, Support, Sales, Staff, Client
- Полная изоляция данных между тенантами
- JWT аутентификация с Supabase Auth

## 🛠 Быстрый старт

### 1. Установка зависимостей
```bash
npm install
```

### 2. Настройка переменных окружения
Скопируйте `.env.example` в `.env.local` и настройте:

```bash
cp .env.example .env.local
```

**Важно**: Замените demo значения на реальные:
- Создайте проект в [Supabase](https://supabase.com)
- Получите API ключи в Settings > API
- Обновите `.env.local` с вашими данными

### 3. Настройка базы данных
```bash
# Установите Supabase CLI
npm install -g supabase

# Войдите в Supabase
supabase login

# Свяжите проект
supabase link --project-ref YOUR_PROJECT_ID

# Примените миграции
supabase db push
```

### 4. Запуск приложения
```bash
npm run dev
```

Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## 🎯 Что можно делать прямо сейчас

### 1. Просмотр лендинга
- Откройте главную страницу
- Изучите функции платформы
- Попробуйте навигацию

### 2. Регистрация и вход
- Создайте аккаунт через `/auth/signup`
- Войдите через `/auth/signin`
- Изучите dashboard

### 3. Управление клиентами
- Добавьте тестовых клиентов
- Настройте их профили
- Проверьте фильтрацию и поиск

### 4. SIP аккаунты
- Создайте SIP аккаунты для клиентов
- Используйте Live Preview для просмотра конфигурации
- Копируйте учетные данные

### 5. Call Flow Builder
- Создайте IVR сценарии
- Используйте визуальный редактор
- Тестируйте с Live Preview

## 🔧 Дополнительная настройка

### Интеграция с SignalWire
1. Создайте аккаунт в [SignalWire](https://signalwire.com)
2. Получите API ключи
3. Обновите переменные в `.env.local`
4. Настройте webhooks для событий

### Интеграция со Stripe
1. Создайте аккаунт в [Stripe](https://stripe.com)
2. Получите API ключи (test/live)
3. Настройте webhooks для платежей
4. Обновите переменные в `.env.local`

### Настройка email уведомлений
1. Настройте SMTP сервер (Gmail, SendGrid, etc.)
2. Обновите SMTP настройки в `.env.local`
3. Протестируйте отправку уведомлений

## 📁 Структура проекта

```
├── app/                    # Next.js App Router
│   ├── auth/              # Страницы аутентификации
│   ├── dashboard/         # Страницы dashboard
│   └── globals.css        # Глобальные стили
├── components/            # React компоненты
│   ├── ui/               # Базовые UI компоненты
│   ├── dashboard/        # Компоненты dashboard
│   ├── clients/          # Управление клиентами
│   ├── sip-accounts/     # Управление SIP
│   ├── call-flows/       # Call Flow Builder
│   ├── live-preview/     # Live Preview
│   └── providers/        # Context провайдеры
├── lib/                  # Утилиты и библиотеки
├── types/                # TypeScript типы
├── supabase/             # Миграции и функции
└── public/               # Статические файлы
```

## 🔐 Система ролей

### Provider (Уровень 5)
- Полный доступ ко всем тенантам
- Управление платформой
- Системная аналитика

### Reseller (Уровень 4)
- Управление своим тенантом
- Создание клиентов и пользователей
- Биллинг и отчеты

### Admin (Уровень 3)
- Полный доступ в рамках тенанта
- Управление пользователями
- Конфигурация системы

### Support (Уровень 2)
- Управление тикетами
- Просмотр клиентов
- Техническая поддержка

### Sales (Уровень 2)
- Управление клиентами
- Продажи и биллинг
- CRM функции

### Staff (Уровень 1)
- Ограниченный доступ
- Базовые операции
- Просмотр данных

### Client (Уровень 0)
- Доступ только к своим данным
- Управление SIP аккаунтами
- Создание тикетов

## 🚀 Следующие шаги

### 1. Настройка продакшена
- Следуйте инструкциям в `DEPLOYMENT.md`
- Настройте домен и SSL
- Конфигурируйте мониторинг

### 2. Дополнительные модули
- DID Numbers Management
- Billing & Invoicing
- Ticket System
- Reports & Analytics
- API Keys Management

### 3. Интеграции
- SignalWire для SIP сервисов
- Stripe для платежей
- Email/SMS провайдеры
- Мониторинг и логирование

## 📚 Документация

- **API Examples**: `API_EXAMPLES.md`
- **Deployment Guide**: `DEPLOYMENT.md`
- **README**: `README.md`

## 🆘 Поддержка

Если у вас возникли вопросы:
1. Проверьте документацию
2. Изучите примеры в коде
3. Создайте issue в репозитории

## 🎉 Готово!

Ваша SPaaS платформа готова к использованию! Начните с создания тестовых данных и изучения функций.

**Удачи в разработке! 🚀**
