'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { 
  Phone, 
  Users, 
  CreditCard, 
  Shield, 
  Zap, 
  Globe,
  CheckCircle,
  ArrowRight,
  Menu,
  X
} from 'lucide-react'

export function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const features = [
    {
      icon: Phone,
      title: 'SIP Management',
      description: 'Complete SIP account management with real-time provisioning and monitoring'
    },
    {
      icon: Users,
      title: 'Multi-Tenant',
      description: 'Secure multi-tenant architecture with complete data isolation'
    },
    {
      icon: CreditCard,
      title: 'Billing & Invoicing',
      description: 'Automated billing, invoicing, and payment processing with Stripe integration'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Row-level security, JWT authentication, and role-based access control'
    },
    {
      icon: Zap,
      title: 'Real-time Updates',
      description: 'Live updates for call flows, billing, and system notifications'
    },
    {
      icon: Globe,
      title: 'White-label Ready',
      description: 'Fully customizable branding and domain configuration for resellers'
    }
  ]

  const benefits = [
    'Complete SIP/VoIP platform management',
    'Multi-tenant architecture for resellers',
    'Automated billing and invoicing',
    'Real-time call flow builder',
    'Comprehensive support ticket system',
    'API-first architecture',
    'Enterprise-grade security',
    'White-label customization'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gradient">SPaaS Platform</h1>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900">Features</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900">Pricing</a>
              <a href="#contact" className="text-gray-600 hover:text-gray-900">Contact</a>
              <Button
                variant="ghost"
                onClick={() => window.location.href = '/admin'}
                className="text-purple-600 hover:text-purple-700"
              >
                🎮 Demo
              </Button>
              <Link href="/auth/signin">
                <Button variant="outline">Sign In</Button>
              </Link>
              <Link href="/auth/signup">
                <Button>Get Started</Button>
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="text-gray-600 hover:text-gray-900"
              >
                {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <a href="#features" className="block px-3 py-2 text-gray-600 hover:text-gray-900">Features</a>
              <a href="#pricing" className="block px-3 py-2 text-gray-600 hover:text-gray-900">Pricing</a>
              <a href="#contact" className="block px-3 py-2 text-gray-600 hover:text-gray-900">Contact</a>
              <div className="px-3 py-2 space-y-2">
                <Button
                  onClick={() => window.location.href = '/admin'}
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  🎮 Demo Admin Panel
                </Button>
                <Link href="/auth/signin" className="block">
                  <Button variant="outline" className="w-full">Sign In</Button>
                </Link>
                <Link href="/auth/signup" className="block">
                  <Button className="w-full">Get Started</Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            SIP Platform as a Service
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Complete multi-tenant SIP/VoIP platform with billing, support, and real-time management. 
            Perfect for telecom resellers and service providers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => window.location.href = '/admin'}
              className="text-lg px-8 py-4 bg-purple-600 hover:bg-purple-700"
            >
              🎮 Demo Admin Panel
            </Button>
            <Button
              size="lg"
              onClick={() => window.location.href = '/dashboard'}
              variant="outline"
              className="text-lg px-8 py-4"
            >
              📊 Full Dashboard
            </Button>
            <Link href="/auth/signup">
              <Button size="lg" variant="ghost" className="text-lg px-8 py-4">
                Start Free Trial
                <ArrowRight className="ml-2" size={20} />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything you need to run a SIP platform
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built for telecom professionals who need enterprise-grade features with the simplicity of SaaS
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <feature.icon className="h-12 w-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why choose SPaaS Platform?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Our platform is designed specifically for telecom resellers and service providers 
                who need a complete, scalable solution without the complexity of building from scratch.
              </p>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to get started?</h3>
              <p className="text-gray-600 mb-6">
                Join hundreds of telecom professionals who trust SPaaS Platform for their business.
              </p>
              <Link href="/auth/signup">
                <Button size="lg" className="w-full">
                  Start Your Free Trial
                </Button>
              </Link>
              <p className="text-sm text-gray-500 mt-4 text-center">
                No credit card required • 14-day free trial
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">SPaaS Platform</h3>
              <p className="text-gray-400 mb-4">
                The complete SIP Platform as a Service solution for telecom resellers and service providers.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#features" className="hover:text-white">Features</a></li>
                <li><a href="#pricing" className="hover:text-white">Pricing</a></li>
                <li><a href="#" className="hover:text-white">API Docs</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#contact" className="hover:text-white">Contact</a></li>
                <li><a href="#" className="hover:text-white">Documentation</a></li>
                <li><a href="#" className="hover:text-white">Status</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 SPaaS Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
