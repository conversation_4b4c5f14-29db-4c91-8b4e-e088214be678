# 🔌 SPaaS Platform API Examples

This document provides examples of how to interact with the SPaaS Platform API using various programming languages and tools.

## 🔑 Authentication

All API requests require authentication using JWT tokens from Supabase Auth.

### Getting an Access Token

```javascript
// JavaScript/TypeScript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'https://your-project.supabase.co',
  'your-anon-key'
)

// Sign in to get access token
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

const accessToken = data.session?.access_token
```

### Using the Access Token

```bash
# cURL example
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "apikey: YOUR_SUPABASE_ANON_KEY" \
     https://your-project.supabase.co/rest/v1/clients
```

## 👥 Client Management

### Create a New Client

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('clients')
  .insert({
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '******-0123',
    company: 'Acme Corp',
    address: {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zip: '12345',
      country: 'US'
    }
  })
```

```python
# Python
import requests

headers = {
    'Authorization': f'Bearer {access_token}',
    'apikey': 'your-anon-key',
    'Content-Type': 'application/json'
}

data = {
    'name': 'Acme Corporation',
    'email': '<EMAIL>',
    'phone': '******-0123',
    'company': 'Acme Corp'
}

response = requests.post(
    'https://your-project.supabase.co/rest/v1/clients',
    headers=headers,
    json=data
)
```

### Get All Clients

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('clients')
  .select('*')
  .order('created_at', { ascending: false })
```

```bash
# cURL
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "apikey: YOUR_SUPABASE_ANON_KEY" \
     "https://your-project.supabase.co/rest/v1/clients?select=*&order=created_at.desc"
```

### Update Client

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('clients')
  .update({ 
    status: 'suspended',
    credit_limit: 1000.00 
  })
  .eq('id', 'client-uuid')
```

## 📞 SIP Account Management

### Create SIP Account

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('sip_accounts')
  .insert({
    client_id: 'client-uuid',
    username: 'user123',
    password: 'secure-password',
    domain: 'sip.yourplatform.com',
    display_name: 'John Doe',
    settings: {
      codec: 'G.711',
      dtmf_mode: 'rfc2833',
      nat: true
    }
  })
```

### Get SIP Accounts for Client

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('sip_accounts')
  .select(`
    *,
    client:clients(name, email)
  `)
  .eq('client_id', 'client-uuid')
  .eq('status', 'active')
```

## 📱 DID Number Management

### Purchase DID Number

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('did_numbers')
  .insert({
    number: '+***********',
    country_code: 'US',
    area_code: '555',
    provider: 'SignalWire',
    monthly_cost: 2.00,
    status: 'available'
  })
```

### Assign DID to Client

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('did_numbers')
  .update({
    client_id: 'client-uuid',
    status: 'active'
  })
  .eq('id', 'did-uuid')
```

## 🎯 Call Flow Management

### Create Call Flow

```javascript
// JavaScript/TypeScript
const callFlowData = {
  name: 'Customer Support IVR',
  description: 'Main customer support call flow',
  flow_data: {
    steps: [
      {
        id: '1',
        type: 'greeting',
        name: 'Welcome Message',
        settings: {
          message: 'Thank you for calling. Please hold while we connect you.',
          voice: 'alice',
          language: 'en-US'
        }
      },
      {
        id: '2',
        type: 'menu',
        name: 'Main Menu',
        settings: {
          message: 'Press 1 for Sales, 2 for Support, 0 for Operator',
          timeout: 10,
          retries: 3
        },
        routes: {
          '1': 'sales-queue',
          '2': 'support-queue',
          '0': 'operator'
        }
      }
    ]
  }
}

const { data, error } = await supabase
  .from('call_flows')
  .insert(callFlowData)
```

## 🎫 Ticket Management

### Create Support Ticket

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('tickets')
  .insert({
    client_id: 'client-uuid',
    title: 'SIP Registration Issues',
    description: 'Unable to register SIP account on mobile device',
    priority: 'high',
    status: 'open',
    tags: ['sip', 'mobile', 'registration']
  })
```

### Add Message to Ticket

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('ticket_messages')
  .insert({
    ticket_id: 'ticket-uuid',
    message: 'I have tried restarting the app but the issue persists.',
    is_internal: false
  })
```

## 💰 Billing & Invoicing

### Create Invoice

```javascript
// JavaScript/TypeScript
const invoiceData = {
  client_id: 'client-uuid',
  invoice_number: 'INV-2024-001',
  amount: 150.00,
  tax_amount: 12.00,
  total_amount: 162.00,
  currency: 'USD',
  due_date: '2024-02-15',
  items: [
    {
      description: 'SIP Account - Monthly',
      quantity: 2,
      rate: 25.00,
      amount: 50.00
    },
    {
      description: 'DID Numbers - Monthly',
      quantity: 5,
      rate: 2.00,
      amount: 10.00
    },
    {
      description: 'Usage Charges',
      quantity: 1,
      rate: 90.00,
      amount: 90.00
    }
  ]
}

const { data, error } = await supabase
  .from('invoices')
  .insert(invoiceData)
```

### Get Client Balance

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('clients')
  .select('balance, credit_limit')
  .eq('id', 'client-uuid')
  .single()

const availableCredit = data.credit_limit - Math.abs(data.balance)
```

## 📊 Reporting & Analytics

### Get Usage Statistics

```javascript
// JavaScript/TypeScript
// Get call statistics for the current month
const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1)

const { data, error } = await supabase
  .from('logs')
  .select('*')
  .eq('action', 'call_ended')
  .gte('created_at', startOfMonth.toISOString())
  .order('created_at', { ascending: false })
```

### Revenue Report

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('invoices')
  .select('total_amount, created_at, status')
  .eq('status', 'paid')
  .gte('created_at', '2024-01-01')
  .lte('created_at', '2024-12-31')
```

## 🔐 API Key Management

### Create API Key

```javascript
// JavaScript/TypeScript
const { data, error } = await supabase
  .from('api_keys')
  .insert({
    name: 'Mobile App Integration',
    permissions: {
      clients: ['read', 'write'],
      sip_accounts: ['read'],
      did_numbers: ['read']
    },
    expires_at: '2025-12-31T23:59:59Z'
  })
```

## 🔄 Real-time Subscriptions

### Subscribe to Client Changes

```javascript
// JavaScript/TypeScript
const subscription = supabase
  .channel('client-changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'clients'
    },
    (payload) => {
      console.log('Client changed:', payload)
    }
  )
  .subscribe()
```

### Subscribe to Ticket Updates

```javascript
// JavaScript/TypeScript
const ticketSubscription = supabase
  .channel('ticket-updates')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'tickets',
      filter: 'status=eq.open'
    },
    (payload) => {
      console.log('Ticket updated:', payload)
    }
  )
  .subscribe()
```

## 🧪 Testing Examples

### Unit Test Example (Jest)

```javascript
// __tests__/api/clients.test.js
import { createClient } from '@supabase/supabase-js'

describe('Client API', () => {
  let supabase

  beforeAll(() => {
    supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY
    )
  })

  test('should create a new client', async () => {
    const clientData = {
      name: 'Test Client',
      email: '<EMAIL>'
    }

    const { data, error } = await supabase
      .from('clients')
      .insert(clientData)
      .select()

    expect(error).toBeNull()
    expect(data[0]).toMatchObject(clientData)
  })
})
```

## 🔧 Error Handling

### Common Error Patterns

```javascript
// JavaScript/TypeScript
try {
  const { data, error } = await supabase
    .from('clients')
    .insert(clientData)

  if (error) {
    switch (error.code) {
      case '23505': // Unique constraint violation
        throw new Error('Client with this email already exists')
      case '42501': // Insufficient privilege
        throw new Error('You do not have permission to perform this action')
      default:
        throw new Error(`Database error: ${error.message}`)
    }
  }

  return data
} catch (error) {
  console.error('API Error:', error)
  throw error
}
```

---

For more examples and detailed API documentation, visit the [Supabase Auto-generated API Docs](https://your-project.supabase.co/rest/v1/) for your project.
