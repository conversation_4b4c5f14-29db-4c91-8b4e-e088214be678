import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { ClientsManagement } from '@/components/clients/clients-management'
import { getUserProfile } from '@/lib/supabase-server'

export default async function ClientsPage() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const profile = await getUserProfile(session.user.id)
  
  if (!profile) {
    redirect('/auth/signin')
  }

  // Check if user has permission to view clients
  if (!['provider', 'reseller', 'admin', 'support', 'sales'].includes(profile.role)) {
    redirect('/dashboard')
  }

  return <ClientsManagement profile={profile} />
}
