'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  PhoneCall, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Phone,
  Globe,
  MapPin,
  DollarSign,
  Eye,
  Settings
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { createClientSupabase } from '@/lib/supabase'

interface DidNumber {
  id: string
  number: string
  country_code: string
  area_code: string
  city: string
  state: string
  country: string
  provider: string
  monthly_cost: number
  setup_cost: number
  status: 'available' | 'assigned' | 'porting' | 'suspended'
  assigned_to?: string
  assigned_client?: string
  features: string[]
  created_at: string
  updated_at: string
}

interface Profile {
  id: string
  tenant_id: string
  role: string
  tenant?: {
    name: string
  }
}

interface DidNumbersManagementProps {
  profile: Profile
}

export function DidNumbersManagement({ profile }: DidNumbersManagementProps) {
  const [didNumbers, setDidNumbers] = useState<DidNumber[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [countryFilter, setCountryFilter] = useState<string>('all')
  const [selectedNumber, setSelectedNumber] = useState<DidNumber | null>(null)
  const [showNumberDialog, setShowNumberDialog] = useState(false)
  const [previewNumber, setPreviewNumber] = useState<DidNumber | null>(null)

  const supabase = createClientSupabase()

  useEffect(() => {
    fetchDidNumbers()
  }, [])

  const fetchDidNumbers = async () => {
    try {
      setLoading(true)
      
      // Demo data for now
      const demoNumbers: DidNumber[] = [
        {
          id: '1',
          number: '******-0123',
          country_code: '+1',
          area_code: '555',
          city: 'New York',
          state: 'NY',
          country: 'United States',
          provider: 'SignalWire',
          monthly_cost: 2.50,
          setup_cost: 0.00,
          status: 'assigned',
          assigned_to: 'client-1',
          assigned_client: 'Acme Corp',
          features: ['SMS', 'Voice', 'MMS'],
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          number: '******-0456',
          country_code: '+1',
          area_code: '555',
          city: 'Los Angeles',
          state: 'CA',
          country: 'United States',
          provider: 'Twilio',
          monthly_cost: 3.00,
          setup_cost: 1.00,
          status: 'available',
          features: ['SMS', 'Voice'],
          created_at: '2024-01-16T11:00:00Z',
          updated_at: '2024-01-16T11:00:00Z'
        },
        {
          id: '3',
          number: '+44-20-7946-0958',
          country_code: '+44',
          area_code: '20',
          city: 'London',
          state: 'England',
          country: 'United Kingdom',
          provider: 'Vonage',
          monthly_cost: 4.50,
          setup_cost: 2.00,
          status: 'porting',
          features: ['Voice'],
          created_at: '2024-01-17T12:00:00Z',
          updated_at: '2024-01-17T12:00:00Z'
        },
        {
          id: '4',
          number: '+49-30-12345678',
          country_code: '+49',
          area_code: '30',
          city: 'Berlin',
          state: 'Berlin',
          country: 'Germany',
          provider: 'Deutsche Telekom',
          monthly_cost: 5.00,
          setup_cost: 3.00,
          status: 'suspended',
          features: ['Voice', 'Fax'],
          created_at: '2024-01-18T13:00:00Z',
          updated_at: '2024-01-18T13:00:00Z'
        },
        {
          id: '5',
          number: '******-0789',
          country_code: '+1',
          area_code: '555',
          city: 'Chicago',
          state: 'IL',
          country: 'United States',
          provider: 'SignalWire',
          monthly_cost: 2.50,
          setup_cost: 0.00,
          status: 'assigned',
          assigned_to: 'client-2',
          assigned_client: 'TechStart Inc',
          features: ['SMS', 'Voice', 'MMS', 'Toll-Free'],
          created_at: '2024-01-19T14:00:00Z',
          updated_at: '2024-01-19T14:00:00Z'
        }
      ]
      
      setDidNumbers(demoNumbers)
    } catch (error) {
      console.error('Error fetching DID numbers:', error)
      toast.error('Failed to load DID numbers')
    } finally {
      setLoading(false)
    }
  }

  const filteredNumbers = didNumbers.filter(number => {
    const matchesSearch = number.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         number.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         number.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (number.assigned_client && number.assigned_client.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesStatus = statusFilter === 'all' || number.status === statusFilter
    const matchesCountry = countryFilter === 'all' || number.country === countryFilter
    
    return matchesSearch && matchesStatus && matchesCountry
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'assigned':
        return 'bg-blue-100 text-blue-800'
      case 'porting':
        return 'bg-yellow-100 text-yellow-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const countries = [...new Set(didNumbers.map(n => n.country))]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">DID Numbers</h1>
          <p className="text-gray-600 mt-1">
            Manage your Direct Inward Dialing numbers and assignments
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              setSelectedNumber(null)
              setShowNumberDialog(true)
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add DID Number
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <PhoneCall className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Numbers</p>
                <p className="text-2xl font-bold text-gray-900">{didNumbers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Available</p>
                <p className="text-2xl font-bold text-gray-900">
                  {didNumbers.filter(n => n.status === 'available').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Phone className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Assigned</p>
                <p className="text-2xl font-bold text-gray-900">
                  {didNumbers.filter(n => n.status === 'assigned').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Monthly Cost</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(didNumbers.reduce((sum, n) => sum + n.monthly_cost, 0))}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search numbers, cities, or clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="porting">Porting</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>
            <Select value={countryFilter} onValueChange={setCountryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map(country => (
                  <SelectItem key={country} value={country}>{country}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* DID Numbers Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Numbers List */}
        <div className="space-y-4">
          {filteredNumbers.map((number) => (
            <Card key={number.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <PhoneCall className="h-4 w-4 mr-2" />
                      {number.number}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {number.city}, {number.state}, {number.country}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(number.status)}>
                    {number.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Provider:</span>
                      <span className="font-medium">{number.provider}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Monthly Cost:</span>
                      <span className="font-medium">{formatCurrency(number.monthly_cost)}</span>
                    </div>
                    {number.assigned_client && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Assigned to:</span>
                        <span className="font-medium">{number.assigned_client}</span>
                      </div>
                    )}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {number.features.map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewNumber(number)}
                      className="flex-1"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedNumber(number)
                        setShowNumberDialog(true)
                      }}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this DID number?')) {
                          toast.success('DID number deleted successfully')
                        }
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredNumbers.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <PhoneCall className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No DID numbers found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter !== 'all' || countryFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by adding your first DID number'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && countryFilter === 'all' && (
                  <Button
                    onClick={() => {
                      setSelectedNumber(null)
                      setShowNumberDialog(true)
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add DID Number
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Live Preview Panel */}
        <div className="lg:sticky lg:top-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Live Preview
              </CardTitle>
              <CardDescription>
                {previewNumber ? 'DID number configuration preview' : 'Select a DID number to preview'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewNumber ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">{previewNumber.number}</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Location:</span>
                        <p className="font-medium">{previewNumber.city}, {previewNumber.state}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Country:</span>
                        <p className="font-medium">{previewNumber.country}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Provider:</span>
                        <p className="font-medium">{previewNumber.provider}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Status:</span>
                        <Badge className={getStatusColor(previewNumber.status)}>
                          {previewNumber.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Cost Information</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Monthly Cost:</span>
                        <p className="font-medium">{formatCurrency(previewNumber.monthly_cost)}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Setup Cost:</span>
                        <p className="font-medium">{formatCurrency(previewNumber.setup_cost)}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Features</h4>
                    <div className="flex flex-wrap gap-2">
                      {previewNumber.features.map((feature, index) => (
                        <Badge key={index} variant="outline">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {previewNumber.assigned_client && (
                    <div className="space-y-3">
                      <h4 className="font-medium">Assignment</h4>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm">
                          <span className="text-gray-600">Assigned to:</span>
                          <span className="font-medium ml-1">{previewNumber.assigned_client}</span>
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t">
                    <Button
                      onClick={() => {
                        setSelectedNumber(previewNumber)
                        setShowNumberDialog(true)
                      }}
                      className="w-full"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Configure Number
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <PhoneCall className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Click on any DID number to see its configuration preview
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add/Edit DID Number Dialog */}
      <Dialog open={showNumberDialog} onOpenChange={setShowNumberDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedNumber ? 'Edit DID Number' : 'Add New DID Number'}
            </DialogTitle>
            <DialogDescription>
              {selectedNumber
                ? 'Update the DID number configuration and assignment'
                : 'Add a new DID number to your inventory'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="number">Phone Number *</Label>
                <Input
                  id="number"
                  placeholder="******-0123"
                  defaultValue={selectedNumber?.number || ''}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="provider">Provider *</Label>
                <Select defaultValue={selectedNumber?.provider || ''}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SignalWire">SignalWire</SelectItem>
                    <SelectItem value="Twilio">Twilio</SelectItem>
                    <SelectItem value="Vonage">Vonage</SelectItem>
                    <SelectItem value="Deutsche Telekom">Deutsche Telekom</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country *</Label>
                <Select defaultValue={selectedNumber?.country || ''}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="United States">United States</SelectItem>
                    <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                    <SelectItem value="Germany">Germany</SelectItem>
                    <SelectItem value="Canada">Canada</SelectItem>
                    <SelectItem value="Australia">Australia</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State/Region</Label>
                <Input
                  id="state"
                  placeholder="NY, CA, London..."
                  defaultValue={selectedNumber?.state || ''}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="New York, London..."
                  defaultValue={selectedNumber?.city || ''}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="monthly_cost">Monthly Cost ($)</Label>
                <Input
                  id="monthly_cost"
                  type="number"
                  step="0.01"
                  placeholder="2.50"
                  defaultValue={selectedNumber?.monthly_cost || ''}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="setup_cost">Setup Cost ($)</Label>
                <Input
                  id="setup_cost"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  defaultValue={selectedNumber?.setup_cost || ''}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select defaultValue={selectedNumber?.status || 'available'}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="assigned">Assigned</SelectItem>
                  <SelectItem value="porting">Porting</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Features</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {['Voice', 'SMS', 'MMS', 'Fax', 'Toll-Free', 'Local'].map((feature) => (
                  <label key={feature} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      defaultChecked={selectedNumber?.features.includes(feature) || false}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{feature}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="assigned_client">Assigned Client (Optional)</Label>
              <Select defaultValue={selectedNumber?.assigned_client || ''}>
                <SelectTrigger>
                  <SelectValue placeholder="Select client or leave unassigned" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Unassigned</SelectItem>
                  <SelectItem value="Acme Corp">Acme Corp</SelectItem>
                  <SelectItem value="TechStart Inc">TechStart Inc</SelectItem>
                  <SelectItem value="Global Solutions">Global Solutions</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setShowNumberDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                toast.success(selectedNumber ? 'DID number updated successfully' : 'DID number added successfully')
                setShowNumberDialog(false)
                setSelectedNumber(null)
              }}
            >
              {selectedNumber ? 'Update Number' : 'Add Number'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
