'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClientSupabase } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'

interface UserProfile {
  id: string
  tenant_id: string
  email: string
  first_name: string | null
  last_name: string | null
  avatar_url: string | null
  role: 'provider' | 'reseller' | 'admin' | 'support' | 'sales' | 'staff' | 'client'
  permissions: any
  phone: string | null
  is_active: boolean
  tenant?: {
    id: string
    name: string
    domain: string | null
    logo_url: string | null
    primary_color: string | null
    secondary_color: string | null
    status: string
  }
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData?: any) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClientSupabase()

  useEffect(() => {
    // Check if we're in demo mode
    const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                      process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

    if (isDemoMode) {
      // Demo mode - just set loading to false
      setLoading(false)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchUserProfile(session.user.id)
        }
      } catch (error) {
        console.error('Error getting session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchUserProfile(session.user.id)
        } else {
          setProfile(null)
        }

        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          tenant:tenants(*)
        `)
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return
      }

      setProfile(data as UserProfile)
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      // Check if we're in demo mode (no real Supabase connection)
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - simulate successful login
        const demoUser = {
          id: 'demo-user-id',
          email: email,
          user_metadata: {
            first_name: 'Demo',
            last_name: 'User'
          }
        }

        const demoProfile = {
          id: 'demo-user-id',
          tenant_id: 'demo-tenant-id',
          email: email,
          first_name: 'Demo',
          last_name: 'User',
          role: 'admin',
          permissions: {},
          phone: null,
          is_active: true,
          tenant: {
            id: 'demo-tenant-id',
            name: 'Demo Company',
            domain: 'demo.local',
            logo_url: null,
            primary_color: '#3B82F6',
            secondary_color: '#1E40AF',
            status: 'active'
          }
        }

        setUser(demoUser as any)
        setProfile(demoProfile as any)
        toast.success('Successfully signed in! (Demo Mode)')

        // Force redirect using window.location
        setTimeout(() => {
          window.location.replace('/dashboard')
        }, 1000)
        return
      }

      // Real Supabase mode
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        throw error
      }

      toast.success('Successfully signed in!')
      router.push('/dashboard')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in')
      throw error
    }
  }

  const signUp = async (email: string, password: string, userData?: any) => {
    try {
      // Check if we're in demo mode
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - simulate successful signup and auto-login
        toast.success('Account created successfully! (Demo Mode)')
        await signIn(email, password)
        return
      }

      // Real Supabase mode
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      })

      if (error) {
        throw error
      }

      toast.success('Check your email for verification link!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up')
      throw error
    }
  }

  const signOut = async () => {
    try {
      // Check if we're in demo mode
      const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                        process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

      if (isDemoMode) {
        // Demo mode - just clear state
        setUser(null)
        setProfile(null)
        toast.success('Successfully signed out! (Demo Mode)')
        router.push('/')
        return
      }

      // Real Supabase mode
      const { error } = await supabase.auth.signOut()

      if (error) {
        throw error
      }

      setUser(null)
      setProfile(null)
      toast.success('Successfully signed out!')
      router.push('/')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out')
      throw error
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)

      if (error) {
        throw error
      }

      await fetchUserProfile(user.id)
      toast.success('Profile updated successfully!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update profile')
      throw error
    }
  }

  const refreshProfile = async () => {
    if (user) {
      await fetchUserProfile(user.id)
    }
  }

  const value = {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
