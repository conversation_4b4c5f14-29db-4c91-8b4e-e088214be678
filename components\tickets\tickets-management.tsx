'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Ticket, 
  Plus, 
  Search, 
  Filter, 
  MessageSquare, 
  Clock,
  User,
  AlertCircle,
  CheckCircle,
  Eye,
  Edit,
  Send,
  Paperclip
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import { createClientSupabase } from '@/lib/supabase'

interface TicketData {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'waiting_customer' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category: 'technical' | 'billing' | 'general' | 'feature_request'
  client_name: string
  client_email: string
  assigned_to?: string
  created_at: string
  updated_at: string
  last_response?: string
  response_count: number
}

interface Profile {
  id: string
  tenant_id: string
  role: string
  first_name: string
  last_name: string
  tenant?: {
    name: string
  }
}

interface TicketsManagementProps {
  profile: Profile
}

export function TicketsManagement({ profile }: TicketsManagementProps) {
  const [tickets, setTickets] = useState<TicketData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [selectedTicket, setSelectedTicket] = useState<TicketData | null>(null)
  const [showTicketDialog, setShowTicketDialog] = useState(false)
  const [showResponseDialog, setShowResponseDialog] = useState(false)
  const [previewTicket, setPreviewTicket] = useState<TicketData | null>(null)

  const supabase = createClientSupabase()

  useEffect(() => {
    fetchTickets()
  }, [])

  const fetchTickets = async () => {
    try {
      setLoading(true)
      
      // Demo data for now
      const demoTickets: TicketData[] = [
        {
          id: '1',
          title: 'SIP Registration Issues',
          description: 'Unable to register SIP account 1001. Getting 403 Forbidden error.',
          status: 'open',
          priority: 'high',
          category: 'technical',
          client_name: 'John Smith',
          client_email: '<EMAIL>',
          assigned_to: 'Support Team',
          created_at: '2024-01-20T09:00:00Z',
          updated_at: '2024-01-20T09:00:00Z',
          response_count: 0
        },
        {
          id: '2',
          title: 'Billing Inquiry - January Invoice',
          description: 'Question about charges on January invoice. Need clarification on DID number costs.',
          status: 'in_progress',
          priority: 'medium',
          category: 'billing',
          client_name: 'Sarah Johnson',
          client_email: '<EMAIL>',
          assigned_to: 'Billing Team',
          created_at: '2024-01-19T14:30:00Z',
          updated_at: '2024-01-20T10:15:00Z',
          last_response: '2024-01-20T10:15:00Z',
          response_count: 2
        },
        {
          id: '3',
          title: 'Feature Request: Call Recording',
          description: 'Would like to request call recording feature for our SIP accounts.',
          status: 'waiting_customer',
          priority: 'low',
          category: 'feature_request',
          client_name: 'Mike Wilson',
          client_email: '<EMAIL>',
          assigned_to: 'Product Team',
          created_at: '2024-01-18T11:00:00Z',
          updated_at: '2024-01-19T16:45:00Z',
          last_response: '2024-01-19T16:45:00Z',
          response_count: 3
        },
        {
          id: '4',
          title: 'Call Quality Issues',
          description: 'Experiencing poor call quality and dropped calls on DID +1-555-0123.',
          status: 'resolved',
          priority: 'high',
          category: 'technical',
          client_name: 'Lisa Brown',
          client_email: '<EMAIL>',
          assigned_to: 'Technical Team',
          created_at: '2024-01-17T08:15:00Z',
          updated_at: '2024-01-19T17:30:00Z',
          last_response: '2024-01-19T17:30:00Z',
          response_count: 5
        },
        {
          id: '5',
          title: 'Account Setup Assistance',
          description: 'Need help setting up new SIP accounts for our team.',
          status: 'closed',
          priority: 'medium',
          category: 'general',
          client_name: 'David Chen',
          client_email: '<EMAIL>',
          assigned_to: 'Support Team',
          created_at: '2024-01-15T13:20:00Z',
          updated_at: '2024-01-18T12:00:00Z',
          last_response: '2024-01-18T12:00:00Z',
          response_count: 4
        }
      ]
      
      setTickets(demoTickets)
    } catch (error) {
      console.error('Error fetching tickets:', error)
      toast.error('Failed to load tickets')
    } finally {
      setLoading(false)
    }
  }

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.client_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter
    
    return matchesSearch && matchesStatus && matchesPriority
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'waiting_customer':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Support Tickets</h1>
          <p className="text-gray-600 mt-1">
            Manage customer support requests and track resolutions
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              setSelectedTicket(null)
              setShowTicketDialog(true)
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Ticket
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Ticket className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{tickets.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Open</p>
                <p className="text-2xl font-bold text-gray-900">
                  {tickets.filter(t => t.status === 'open').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {tickets.filter(t => t.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-gray-900">
                  {tickets.filter(t => t.status === 'resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Urgent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {tickets.filter(t => t.priority === 'urgent').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search tickets, clients, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="waiting_customer">Waiting Customer</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tickets Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tickets List */}
        <div className="space-y-4">
          {filteredTickets.map((ticket) => (
            <Card key={ticket.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <Ticket className="h-4 w-4 mr-2" />
                      {ticket.title}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      #{ticket.id} • {ticket.client_name} • {formatDate(ticket.created_at)}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col gap-1">
                    <Badge className={getStatusColor(ticket.status)}>
                      {ticket.status.replace('_', ' ')}
                    </Badge>
                    <Badge className={getPriorityColor(ticket.priority)}>
                      {ticket.priority}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {ticket.description}
                  </p>

                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Category:</span>
                      <span className="font-medium capitalize">{ticket.category.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Assigned to:</span>
                      <span className="font-medium">{ticket.assigned_to || 'Unassigned'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Responses:</span>
                      <span className="font-medium flex items-center">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        {ticket.response_count}
                      </span>
                    </div>
                    {ticket.last_response && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Last response:</span>
                        <span className="font-medium">{formatDate(ticket.last_response)}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2 pt-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewTicket(ticket)}
                      className="flex-1"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedTicket(ticket)
                        setShowResponseDialog(true)
                      }}
                    >
                      <MessageSquare className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedTicket(ticket)
                        setShowTicketDialog(true)
                      }}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredTickets.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No tickets found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first support ticket'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && priorityFilter === 'all' && (
                  <Button
                    onClick={() => {
                      setSelectedTicket(null)
                      setShowTicketDialog(true)
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Ticket
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Live Preview Panel */}
        <div className="lg:sticky lg:top-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Ticket Details
              </CardTitle>
              <CardDescription>
                {previewTicket ? 'Ticket information and history' : 'Select a ticket to view details'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewTicket ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">{previewTicket.title}</h3>
                    <div className="flex gap-2 mb-3">
                      <Badge className={getStatusColor(previewTicket.status)}>
                        {previewTicket.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={getPriorityColor(previewTicket.priority)}>
                        {previewTicket.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{previewTicket.description}</p>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Client Information</h4>
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Name:</span>
                        <span className="font-medium">{previewTicket.client_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Email:</span>
                        <span className="font-medium">{previewTicket.client_email}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Ticket Details</h4>
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Category:</span>
                        <span className="font-medium capitalize">{previewTicket.category.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Assigned to:</span>
                        <span className="font-medium">{previewTicket.assigned_to || 'Unassigned'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Created:</span>
                        <span className="font-medium">{formatDate(previewTicket.created_at)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Updated:</span>
                        <span className="font-medium">{formatDate(previewTicket.updated_at)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Responses:</span>
                        <span className="font-medium">{previewTicket.response_count}</span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t space-y-2">
                    <Button
                      onClick={() => {
                        setSelectedTicket(previewTicket)
                        setShowResponseDialog(true)
                      }}
                      className="w-full"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Add Response
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedTicket(previewTicket)
                        setShowTicketDialog(true)
                      }}
                      className="w-full"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Ticket
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Click on any ticket to see its details and history
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
