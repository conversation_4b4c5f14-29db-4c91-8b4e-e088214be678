'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createClientSupabase } from '@/lib/supabase'
import { LivePreview } from '@/components/live-preview/live-preview'
import toast from 'react-hot-toast'
import { 
  Save, 
  ArrowLeft, 
  Plus, 
  Trash2, 
  Move,
  Phone,
  MessageSquare,
  Music,
  Users,
  Settings
} from 'lucide-react'

interface CallFlow {
  id: string
  name: string
  description: string | null
  flow_data: any
  is_active: boolean
}

interface CallFlowStep {
  id: string
  type: 'greeting' | 'menu' | 'queue' | 'transfer' | 'hangup' | 'record'
  name: string
  description?: string
  settings: any
  next?: string
  routes?: Record<string, string>
}

interface CallFlowBuilderProps {
  flow?: CallFlow | null
  onSaved: () => void
  onCancel: () => void
}

export function CallFlowBuilder({ flow, onSaved, onCancel }: CallFlowBuilderProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: false
  })
  const [steps, setSteps] = useState<CallFlowStep[]>([])
  const [selectedStep, setSelectedStep] = useState<CallFlowStep | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const supabase = createClientSupabase()

  useEffect(() => {
    if (flow) {
      setFormData({
        name: flow.name,
        description: flow.description || '',
        is_active: flow.is_active
      })
      setSteps(flow.flow_data?.steps || [])
    } else {
      // Initialize with a default greeting step
      const defaultStep: CallFlowStep = {
        id: '1',
        type: 'greeting',
        name: 'Welcome Message',
        description: 'Initial greeting for callers',
        settings: {
          message: 'Thank you for calling. Please hold while we connect you.',
          voice: 'alice',
          language: 'en-US'
        }
      }
      setSteps([defaultStep])
    }
  }, [flow])

  const stepTypes = [
    { value: 'greeting', label: 'Greeting', icon: MessageSquare, description: 'Play a welcome message' },
    { value: 'menu', label: 'Menu', icon: Settings, description: 'Interactive voice menu' },
    { value: 'queue', label: 'Queue', icon: Users, description: 'Hold queue with music' },
    { value: 'transfer', label: 'Transfer', icon: Phone, description: 'Transfer to number/extension' },
    { value: 'record', label: 'Record', icon: Music, description: 'Record caller message' },
    { value: 'hangup', label: 'Hangup', icon: Phone, description: 'End the call' }
  ]

  const addStep = () => {
    const newStep: CallFlowStep = {
      id: (steps.length + 1).toString(),
      type: 'greeting',
      name: `Step ${steps.length + 1}`,
      settings: {}
    }
    setSteps([...steps, newStep])
    setSelectedStep(newStep)
  }

  const updateStep = (stepId: string, updates: Partial<CallFlowStep>) => {
    setSteps(steps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ))
    
    if (selectedStep?.id === stepId) {
      setSelectedStep({ ...selectedStep, ...updates })
    }
  }

  const deleteStep = (stepId: string) => {
    setSteps(steps.filter(step => step.id !== stepId))
    if (selectedStep?.id === stepId) {
      setSelectedStep(null)
    }
  }

  const moveStep = (stepId: string, direction: 'up' | 'down') => {
    const index = steps.findIndex(step => step.id === stepId)
    if (index === -1) return

    const newSteps = [...steps]
    const targetIndex = direction === 'up' ? index - 1 : index + 1

    if (targetIndex >= 0 && targetIndex < steps.length) {
      [newSteps[index], newSteps[targetIndex]] = [newSteps[targetIndex], newSteps[index]]
      setSteps(newSteps)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (steps.length === 0) {
      newErrors.steps = 'At least one step is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const flowData = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        is_active: formData.is_active,
        flow_data: {
          steps,
          version: '1.0'
        }
      }

      if (flow) {
        // Update existing flow
        const { error } = await supabase
          .from('call_flows')
          .update(flowData)
          .eq('id', flow.id)

        if (error) throw error

        toast.success('Call flow updated successfully!')
      } else {
        // Create new flow
        const { error } = await supabase
          .from('call_flows')
          .insert(flowData)

        if (error) throw error

        toast.success('Call flow created successfully!')
      }

      onSaved()
    } catch (error: any) {
      console.error('Error saving call flow:', error)
      toast.error(error.message || 'Failed to save call flow')
    } finally {
      setLoading(false)
    }
  }

  const renderStepSettings = (step: CallFlowStep) => {
    switch (step.type) {
      case 'greeting':
        return (
          <div className="space-y-4">
            <div>
              <Label>Message</Label>
              <Textarea
                value={step.settings.message || ''}
                onChange={(e) => updateStep(step.id, {
                  settings: { ...step.settings, message: e.target.value }
                })}
                placeholder="Enter the greeting message"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Voice</Label>
                <Select
                  value={step.settings.voice || 'alice'}
                  onValueChange={(value) => updateStep(step.id, {
                    settings: { ...step.settings, voice: value }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="alice">Alice (Female)</SelectItem>
                    <SelectItem value="bob">Bob (Male)</SelectItem>
                    <SelectItem value="charlie">Charlie (Male)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Language</Label>
                <Select
                  value={step.settings.language || 'en-US'}
                  onValueChange={(value) => updateStep(step.id, {
                    settings: { ...step.settings, language: value }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="en-GB">English (UK)</SelectItem>
                    <SelectItem value="es-ES">Spanish</SelectItem>
                    <SelectItem value="fr-FR">French</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )

      case 'menu':
        return (
          <div className="space-y-4">
            <div>
              <Label>Menu Message</Label>
              <Textarea
                value={step.settings.message || ''}
                onChange={(e) => updateStep(step.id, {
                  settings: { ...step.settings, message: e.target.value }
                })}
                placeholder="Press 1 for Sales, 2 for Support..."
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Timeout (seconds)</Label>
                <Input
                  type="number"
                  value={step.settings.timeout || 10}
                  onChange={(e) => updateStep(step.id, {
                    settings: { ...step.settings, timeout: parseInt(e.target.value) || 10 }
                  })}
                />
              </div>
              <div>
                <Label>Max Retries</Label>
                <Input
                  type="number"
                  value={step.settings.retries || 3}
                  onChange={(e) => updateStep(step.id, {
                    settings: { ...step.settings, retries: parseInt(e.target.value) || 3 }
                  })}
                />
              </div>
            </div>
          </div>
        )

      case 'transfer':
        return (
          <div className="space-y-4">
            <div>
              <Label>Transfer To</Label>
              <Input
                value={step.settings.destination || ''}
                onChange={(e) => updateStep(step.id, {
                  settings: { ...step.settings, destination: e.target.value }
                })}
                placeholder="+1-555-0123 or extension"
              />
            </div>
            <div>
              <Label>Transfer Message</Label>
              <Input
                value={step.settings.message || ''}
                onChange={(e) => updateStep(step.id, {
                  settings: { ...step.settings, message: e.target.value }
                })}
                placeholder="Please hold while we transfer your call"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center text-gray-500 py-8">
            Select a step type to configure settings
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {flow ? 'Edit Call Flow' : 'Create Call Flow'}
            </h1>
            <p className="text-gray-600 mt-1">Design your IVR call flow</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Saving...' : 'Save Flow'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Flow Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Flow Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Customer Support IVR"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe this call flow"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </CardContent>
          </Card>

          {/* Steps List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Flow Steps</CardTitle>
                <Button size="sm" onClick={addStep}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add Step
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {steps.map((step, index) => {
                  const StepIcon = stepTypes.find(t => t.value === step.type)?.icon || Settings
                  return (
                    <div
                      key={step.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedStep?.id === step.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedStep(step)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <StepIcon className="h-4 w-4" />
                          <div>
                            <p className="font-medium text-sm">{step.name}</p>
                            <p className="text-xs text-gray-500">{step.type}</p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              moveStep(step.id, 'up')
                            }}
                            disabled={index === 0}
                          >
                            <Move className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteStep(step.id)
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
                {steps.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <Settings className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No steps added yet</p>
                    <Button size="sm" onClick={addStep} className="mt-2">
                      Add First Step
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Step Configuration */}
        <div className="lg:col-span-1">
          <Card className="h-fit">
            <CardHeader>
              <CardTitle>
                {selectedStep ? `Configure: ${selectedStep.name}` : 'Step Configuration'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedStep ? (
                <div className="space-y-4">
                  <div>
                    <Label>Step Name</Label>
                    <Input
                      value={selectedStep.name}
                      onChange={(e) => updateStep(selectedStep.id, { name: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label>Step Type</Label>
                    <Select
                      value={selectedStep.type}
                      onValueChange={(value) => updateStep(selectedStep.id, { 
                        type: value as any,
                        settings: {} // Reset settings when type changes
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {stepTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center space-x-2">
                              <type.icon className="h-4 w-4" />
                              <span>{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {renderStepSettings(selectedStep)}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <Settings className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>Select a step to configure its settings</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Live Preview */}
        <div className="lg:col-span-1">
          <LivePreview
            type="call-flow"
            data={{ steps, name: formData.name }}
            onPreview={(data) => console.log('Preview data:', data)}
          />
        </div>
      </div>
    </div>
  )
}
