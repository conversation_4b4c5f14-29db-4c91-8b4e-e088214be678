# ⚡ SPaaS Platform - Быстрый старт

## 🎯 Что это?

**SPaaS Platform** - это полнофункциональная SIP Platform as a Service с:
- 🏢 **Мультитенантностью** - полная изоляция данных
- 🔐 **Ролевой системой** - 7 уровней доступа
- 📞 **SIP управлением** - создание и настройка аккаунтов
- 🎛️ **Call Flow Builder** - визуальный конструктор IVR
- 👁️ **Live Preview** - предварительный просмотр в реальном времени
- 💳 **Биллингом** - интеграция со Stripe
- 🎫 **Поддержкой** - система тикетов

## 🚀 Запуск за 3 минуты

### 1. Установка
```bash
npm install
```

### 2. Запуск
```bash
npm run dev
```

### 3. Открыть
Перейдите на [http://localhost:3000](http://localhost:3000)

## 🎮 Что попробовать

### 📱 Лендинг страница
- Современный дизайн
- Описание функций
- Responsive интерфейс

### 🔐 Аутентификация
- Регистрация: `/auth/signup`
- Вход: `/auth/signin`
- Демо данные работают без Supabase

### 📊 Dashboard
- Статистика в реальном времени
- Быстрые действия
- Системный статус

### 👥 Управление клиентами
- Добавление клиентов
- Поиск и фильтрация
- Редактирование профилей

### 📞 SIP аккаунты
- Создание аккаунтов
- Генерация учетных данных
- Live Preview конфигурации

### 🎛️ Call Flow Builder
- Визуальный редактор IVR
- Различные типы шагов
- Интерактивная симуляция

## 🔧 Настройка для продакшена

### 1. Supabase
```bash
# Создайте проект на supabase.com
# Скопируйте URL и ключи в .env.local
NEXT_PUBLIC_SUPABASE_URL=your_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key
```

### 2. База данных
```bash
# Установите Supabase CLI
npm install -g supabase

# Примените миграции
supabase db push
```

### 3. Интеграции
```bash
# SignalWire для SIP
SIGNALWIRE_PROJECT_ID=your_id
SIGNALWIRE_API_TOKEN=your_token

# Stripe для биллинга
STRIPE_SECRET_KEY=your_key
```

## 📁 Структура проекта

```
├── app/                    # Next.js страницы
│   ├── auth/              # Аутентификация
│   └── dashboard/         # Основной интерфейс
├── components/            # React компоненты
│   ├── ui/               # Базовые компоненты
│   ├── clients/          # Управление клиентами
│   ├── sip-accounts/     # SIP аккаунты
│   ├── call-flows/       # Call Flow Builder
│   └── live-preview/     # Live Preview
├── lib/                  # Утилиты
├── supabase/             # База данных
└── types/                # TypeScript типы
```

## 🎨 Основные компоненты

### Dashboard Overview
```typescript
// components/dashboard/dashboard-overview.tsx
- Статистика в реальном времени
- Быстрые действия
- Системный статус
```

### Clients Management
```typescript
// components/clients/clients-management.tsx
- CRUD операции
- Поиск и фильтрация
- Управление балансом
```

### SIP Accounts
```typescript
// components/sip-accounts/sip-accounts-management.tsx
- Создание аккаунтов
- Настройки кодеков
- Live Preview
```

### Call Flow Builder
```typescript
// components/call-flows/call-flow-builder.tsx
- Визуальный редактор
- Различные типы шагов
- Интерактивная симуляция
```

### Live Preview
```typescript
// components/live-preview/live-preview.tsx
- Реальное время предварительного просмотра
- Поддержка разных типов контента
- Интерактивное тестирование
```

## 🔐 Система ролей

```
Provider (5)    → Полный доступ ко всем тенантам
Reseller (4)    → Управление своим тенантом
Admin (3)       → Полный доступ в тенанте
Support (2)     → Тикеты и поддержка
Sales (2)       → Клиенты и продажи
Staff (1)       → Ограниченный доступ
Client (0)      → Только свои данные
```

## 🎯 Live Preview функции

### Call Flow Preview
- Пошаговая симуляция IVR
- Интерактивное тестирование
- Визуализация потока

### SIP Config Preview
- Просмотр конфигурации
- Копирование настроек
- Статус регистрации

### Invoice Preview
- Предварительный просмотр счетов
- Форматирование PDF
- Детализация услуг

## 📚 Документация

- **README.md** - Полная документация
- **SETUP_GUIDE.md** - Детальная настройка
- **DEPLOYMENT.md** - Деплой в продакшен
- **API_EXAMPLES.md** - Примеры API
- **PROJECT_SUMMARY.md** - Обзор проекта

## 🛠️ Технологии

### Frontend
- **Next.js 14** - React фреймворк
- **TypeScript** - Типобезопасность
- **Tailwind CSS** - Стилизация
- **Shadcn/ui** - UI компоненты

### Backend
- **Supabase** - BaaS платформа
- **PostgreSQL** - База данных
- **Row-Level Security** - Безопасность
- **Edge Functions** - Серверные функции

### Интеграции
- **SignalWire** - SIP сервисы
- **Stripe** - Платежи
- **Email/SMS** - Уведомления

## 🚀 Следующие шаги

### Для разработчиков
1. Изучите код компонентов
2. Добавьте новые функции
3. Настройте интеграции
4. Деплойте в продакшен

### Для бизнеса
1. Демонстрируйте клиентам
2. Настройте брендинг
3. Добавьте клиентов
4. Запустите биллинг

## 💡 Полезные команды

```bash
# Разработка
npm run dev              # Запуск dev сервера
npm run build           # Сборка для продакшена
npm run start           # Запуск prod сервера

# Линтинг
npm run lint            # Проверка кода
npm run type-check      # Проверка типов

# База данных
npm run db:migrate      # Применить миграции
npm run db:generate-types # Генерация типов
```

## 🎉 Готово!

Ваша SPaaS платформа готова к использованию!

**Начните с изучения интерфейса и создания тестовых данных.**

---

**Удачи! 🚀**
