import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { getUserProfile } from '@/lib/supabase-server'

export default async function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const profile = await getUserProfile(session.user.id)
  
  if (!profile) {
    redirect('/auth/signin')
  }

  return (
    <DashboardLayout profile={profile}>
      {children}
    </DashboardLayout>
  )
}
