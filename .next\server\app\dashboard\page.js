/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLz80ZGJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcdGVzdFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cdashboard%5Cdashboard-layout.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cdashboard%5Cdashboard-layout.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/dashboard-layout.tsx */ \"(ssr)/./components/dashboard/dashboard-layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q2NvbXBvbmVudHMlNUNkYXNoYm9hcmQlNUNkYXNoYm9hcmQtbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcGFhcy1wbGF0Zm9ybS8/ZTQyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhQXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXHRlc3RcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcZGFzaGJvYXJkLWxheW91dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cdashboard%5Cdashboard-layout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/auth-provider.tsx */ \"(ssr)/./components/providers/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/theme-provider.tsx */ \"(ssr)/./components/providers/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q2NvbXBvbmVudHMlNUNwcm92aWRlcnMlNUNhdXRoLXByb3ZpZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNjb21wb25lbnRzJTVDcHJvdmlkZXJzJTVDdGhlbWUtcHJvdmlkZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSFAlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDdGVzdCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hQJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q3Rlc3QlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBK0g7QUFDL0gsOExBQWdJO0FBQ2hJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3BhYXMtcGxhdGZvcm0vP2I2ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIUFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx0ZXN0XFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIUFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx0ZXN0XFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXHRoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSFBcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcdGVzdFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaG90LXRvYXN0XFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Ccomponents%5Cproviders%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_overview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-overview */ \"(ssr)/./components/dashboard/dashboard-overview.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/dashboard-layout */ \"(ssr)/./components/dashboard/dashboard-layout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardPage() {\n    const { user, profile, loading } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if we're in demo mode\n    const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only redirect if not in demo mode and not authenticated\n        if (!isDemoMode && !loading && !user) {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        user,\n        loading,\n        router,\n        isDemoMode\n    ]);\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    // In demo mode, create a demo profile if none exists\n    const demoProfile = profile || {\n        id: \"demo-user-id\",\n        tenant_id: \"demo-tenant-id\",\n        email: \"<EMAIL>\",\n        first_name: \"Demo\",\n        last_name: \"User\",\n        role: \"admin\",\n        permissions: {},\n        phone: null,\n        is_active: true,\n        tenant: {\n            id: \"demo-tenant-id\",\n            name: \"Demo Company\",\n            domain: \"demo.local\",\n            logo_url: null,\n            primary_color: \"#3B82F6\",\n            secondary_color: \"#1E40AF\",\n            status: \"active\"\n        }\n    };\n    // Don't render if not authenticated and not in demo mode\n    if (!isDemoMode && !user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_5__.DashboardLayout, {\n        profile: demoProfile,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_overview__WEBPACK_IMPORTED_MODULE_4__.DashboardOverview, {\n            profile: demoProfile\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-layout.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-layout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(ssr)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone-call.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,CreditCard,Key,LayoutDashboard,LogOut,Menu,Phone,PhoneCall,Settings,Ticket,Users,Workflow,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\n\n\nfunction DashboardLayout({ children, profile }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { signOut } = (0,_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"Clients\",\n            href: \"/dashboard/clients\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"SIP Accounts\",\n            href: \"/dashboard/sip-accounts\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"DID Numbers\",\n            href: \"/dashboard/did-numbers\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Call Flows\",\n            href: \"/dashboard/call-flows\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            name: \"Billing\",\n            href: \"/dashboard/billing\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            name: \"Tickets\",\n            href: \"/dashboard/tickets\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Reports\",\n            href: \"/dashboard/reports\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            name: \"API Keys\",\n            href: \"/dashboard/api-keys\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: _barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        }\n    ];\n    // Filter navigation based on user role\n    const filteredNavigation = navigation.filter((item)=>{\n        if (profile.role === \"client\") {\n            return [\n                \"Dashboard\",\n                \"SIP Accounts\",\n                \"DID Numbers\",\n                \"Tickets\",\n                \"Settings\"\n            ].includes(item.name);\n        }\n        if (profile.role === \"staff\") {\n            return [\n                \"Dashboard\",\n                \"Clients\",\n                \"Tickets\",\n                \"Settings\"\n            ].includes(item.name);\n        }\n        return true // Admin, reseller, provider see all\n        ;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-0 flex z-40 md:hidden\", sidebarOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-0 pt-5 pb-4 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center px-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gradient\",\n                                            children: \"SPaaS Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 px-2 space-y-1\",\n                                        children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(pathname === item.href ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\", \"group flex items-center px-2 py-2 text-sm font-medium rounded-md\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(pathname === item.href ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\", \"mr-3 flex-shrink-0 h-5 w-5\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    item.name\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center flex-shrink-0 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gradient\",\n                                        children: \"SPaaS Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"mt-5 flex-1 px-2 space-y-1\",\n                                    children: filteredNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(pathname === item.href ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\", \"group flex items-center px-2 py-2 text-sm font-medium rounded-md\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(pathname === item.href ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\", \"mr-3 flex-shrink-0 h-5 w-5\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: [\n                                                    profile.first_name?.[0],\n                                                    profile.last_name?.[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    profile.first_name,\n                                                    \" \",\n                                                    profile.last_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium\", (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getRoleColor)(profile.role)),\n                                                children: profile.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            onClick: ()=>setSidebarOpen(true),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    ( false || \"https://demo.supabase.co\".includes(\"demo\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-600 text-white px-4 py-2 text-center text-sm\",\n                        children: [\n                            \"\\uD83C\\uDFAE \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Demo Mode\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 16\n                            }, this),\n                            \" - This is a demonstration. Data is not persistent.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"underline ml-2\",\n                                children: \"Setup Supabase for full functionality\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"SPaaS Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 hover:text-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: profile.tenant?.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: signOut,\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_CreditCard_Key_LayoutDashboard_LogOut_Menu_Phone_PhoneCall_Settings_Ticket_Users_Workflow_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-overview.tsx":
/*!*****************************************************!*\
  !*** ./components/dashboard/dashboard-overview.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardOverview: () => (/* binding */ DashboardOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone-call.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,DollarSign,Phone,PhoneCall,Ticket,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardOverview auto */ \n\n\n\n\n\n\nfunction DashboardOverview({ profile }) {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalClients: 0,\n        activeSipAccounts: 0,\n        totalDidNumbers: 0,\n        openTickets: 0,\n        monthlyRevenue: 0,\n        recentActivity: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.createClientSupabase)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardStats();\n    }, []);\n    const fetchDashboardStats = async ()=>{\n        try {\n            // Check if we're in demo mode\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - return mock data\n                setStats({\n                    totalClients: 12,\n                    activeSipAccounts: 28,\n                    totalDidNumbers: 15,\n                    openTickets: 3,\n                    monthlyRevenue: 2450.00,\n                    recentActivity: [\n                        {\n                            id: \"1\",\n                            action: \"client_created\",\n                            resource_type: \"client\",\n                            resource_id: \"demo-1\",\n                            created_at: new Date().toISOString(),\n                            details: {\n                                client_name: \"Demo Client\"\n                            }\n                        },\n                        {\n                            id: \"2\",\n                            action: \"sip_account_created\",\n                            resource_type: \"sip_account\",\n                            resource_id: \"demo-2\",\n                            created_at: new Date(Date.now() - 3600000).toISOString(),\n                            details: {\n                                username: \"demo_user\"\n                            }\n                        },\n                        {\n                            id: \"3\",\n                            action: \"call_flow_updated\",\n                            resource_type: \"call_flow\",\n                            resource_id: \"demo-3\",\n                            created_at: new Date(Date.now() - 7200000).toISOString(),\n                            details: {\n                                flow_name: \"Customer Support\"\n                            }\n                        }\n                    ]\n                });\n                setLoading(false);\n                return;\n            }\n            // Real Supabase mode\n            const [clientsResult, sipAccountsResult, didNumbersResult, ticketsResult, invoicesResult, logsResult] = await Promise.all([\n                supabase.from(\"clients\").select(\"id\", {\n                    count: \"exact\"\n                }),\n                supabase.from(\"sip_accounts\").select(\"id\", {\n                    count: \"exact\"\n                }).eq(\"status\", \"active\"),\n                supabase.from(\"did_numbers\").select(\"id\", {\n                    count: \"exact\"\n                }),\n                supabase.from(\"tickets\").select(\"id\", {\n                    count: \"exact\"\n                }).eq(\"status\", \"open\"),\n                supabase.from(\"invoices\").select(\"total_amount\").eq(\"status\", \"paid\").gte(\"created_at\", new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),\n                supabase.from(\"logs\").select(\"*\").order(\"created_at\", {\n                    ascending: false\n                }).limit(5)\n            ]);\n            const monthlyRevenue = invoicesResult.data?.reduce((sum, invoice)=>sum + (invoice.total_amount || 0), 0) || 0;\n            setStats({\n                totalClients: clientsResult.count || 0,\n                activeSipAccounts: sipAccountsResult.count || 0,\n                totalDidNumbers: didNumbersResult.count || 0,\n                openTickets: ticketsResult.count || 0,\n                monthlyRevenue,\n                recentActivity: logsResult.data || []\n            });\n        } catch (error) {\n            console.error(\"Error fetching dashboard stats:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const statCards = [\n        {\n            title: \"Total Clients\",\n            value: stats.totalClients,\n            icon: _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-blue-600\",\n            bgColor: \"bg-blue-100\",\n            change: \"+12%\",\n            changeType: \"increase\"\n        },\n        {\n            title: \"Active SIP Accounts\",\n            value: stats.activeSipAccounts,\n            icon: _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"text-green-600\",\n            bgColor: \"bg-green-100\",\n            change: \"+8%\",\n            changeType: \"increase\"\n        },\n        {\n            title: \"DID Numbers\",\n            value: stats.totalDidNumbers,\n            icon: _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"text-purple-600\",\n            bgColor: \"bg-purple-100\",\n            change: \"+5%\",\n            changeType: \"increase\"\n        },\n        {\n            title: \"Open Tickets\",\n            value: stats.openTickets,\n            icon: _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"text-orange-600\",\n            bgColor: \"bg-orange-100\",\n            change: \"-3%\",\n            changeType: \"decrease\"\n        },\n        {\n            title: \"Monthly Revenue\",\n            value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(stats.monthlyRevenue),\n            icon: _barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-emerald-600\",\n            bgColor: \"bg-emerald-100\",\n            change: \"+15%\",\n            changeType: \"increase\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    ...Array(4)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 15\n                        }, this)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        profile.first_name,\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: [\n                                        \"Here's what's happening with your \",\n                                        profile.tenant?.name,\n                                        \" platform today.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Tenant\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-semibold text-gray-900\",\n                                    children: profile.tenant?.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                    children: profile.tenant?.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                                children: typeof stat.value === \"string\" ? stat.value : stat.value.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-2\",\n                                                children: [\n                                                    stat.changeType === \"increase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-red-500 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm font-medium ${stat.changeType === \"increase\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                                        children: stat.change\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 ml-1\",\n                                                        children: \"vs last month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-3 rounded-full ${stat.bgColor}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: `h-6 w-6 ${stat.color}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Activity\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Latest actions in your platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.recentActivity.length > 0 ? stats.recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: activity.action\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(activity.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-center py-4\",\n                                        children: \"No recent activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Common tasks to get you started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full justify-start\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add New Client\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full justify-start\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create SIP Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full justify-start\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Purchase DID Number\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full justify-start\",\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Support Ticket\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_DollarSign_Phone_PhoneCall_Ticket_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                \"System Status\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: \"SIP Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                            children: \"Operational\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: \"Billing System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                            children: \"Operational\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: \"API Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                            children: \"Operational\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\dashboard\\\\dashboard-overview.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-overview.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if we're in demo mode\n        const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n        if (isDemoMode) {\n            // Demo mode - just set loading to false\n            setLoading(false);\n            return;\n        }\n        // Get initial session\n        const getInitialSession = async ()=>{\n            try {\n                const { data: { session } } = await supabase.auth.getSession();\n                setUser(session?.user ?? null);\n                if (session?.user) {\n                    await fetchUserProfile(session.user.id);\n                }\n            } catch (error) {\n                console.error(\"Error getting session:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(`\n          *,\n          tenant:tenants(*)\n        `).eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user profile:\", error);\n                return;\n            }\n            setProfile(data);\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        try {\n            // Check if we're in demo mode (no real Supabase connection)\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - simulate successful login\n                const demoUser = {\n                    id: \"demo-user-id\",\n                    email: email,\n                    user_metadata: {\n                        first_name: \"Demo\",\n                        last_name: \"User\"\n                    }\n                };\n                const demoProfile = {\n                    id: \"demo-user-id\",\n                    tenant_id: \"demo-tenant-id\",\n                    email: email,\n                    first_name: \"Demo\",\n                    last_name: \"User\",\n                    role: \"admin\",\n                    permissions: {},\n                    phone: null,\n                    is_active: true,\n                    tenant: {\n                        id: \"demo-tenant-id\",\n                        name: \"Demo Company\",\n                        domain: \"demo.local\",\n                        logo_url: null,\n                        primary_color: \"#3B82F6\",\n                        secondary_color: \"#1E40AF\",\n                        status: \"active\"\n                    }\n                };\n                setUser(demoUser);\n                setProfile(demoProfile);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed in! (Demo Mode)\");\n                // Force redirect to dashboard\n                setTimeout(()=>{\n                    window.location.href = \"/dashboard\";\n                }, 500);\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed in!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign in\");\n            throw error;\n        }\n    };\n    const signUp = async (email, password, userData)=>{\n        try {\n            // Check if we're in demo mode\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - simulate successful signup and auto-login\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Account created successfully! (Demo Mode)\");\n                await signIn(email, password);\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: userData\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Check your email for verification link!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign up\");\n            throw error;\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            // Check if we're in demo mode\n            const isDemoMode =  false || \"https://demo.supabase.co\".includes(\"demo\");\n            if (isDemoMode) {\n                // Demo mode - just clear state\n                setUser(null);\n                setProfile(null);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed out! (Demo Mode)\");\n                router.push(\"/\");\n                return;\n            }\n            // Real Supabase mode\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                throw error;\n            }\n            setUser(null);\n            setProfile(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Successfully signed out!\");\n            router.push(\"/\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to sign out\");\n            throw error;\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return;\n        try {\n            const { error } = await supabase.from(\"users\").update(updates).eq(\"id\", user.id);\n            if (error) {\n                throw error;\n            }\n            await fetchUserProfile(user.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Profile updated successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"Failed to update profile\");\n            throw error;\n        }\n    };\n    const refreshProfile = async ()=>{\n        if (user) {\n            await fetchUserProfile(user.id);\n        }\n    };\n    const value = {\n        user,\n        profile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQUNtQztBQUcxRCxTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcGFhcy1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzL3RoZW1lLXByb3ZpZGVyLnRzeD8xYmUxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gJ25leHQtdGhlbWVzL2Rpc3QvdHlwZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServiceSupabase: () => (/* binding */ createServiceSupabase),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Client-side Supabase client\nconst createClientSupabase = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Service role client (for admin operations)\nconst createServiceSupabase = ()=>{\n    const supabaseUrl = \"https://demo.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Helper function to check user permissions\nfunction hasPermission(userRole, requiredRole) {\n    const roleHierarchy = {\n        \"provider\": 5,\n        \"reseller\": 4,\n        \"admin\": 3,\n        \"support\": 2,\n        \"sales\": 2,\n        \"staff\": 1,\n        \"client\": 0\n    };\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJFO0FBQ3ZCO0FBR3BELDhCQUE4QjtBQUN2QixNQUFNRSx1QkFBdUIsSUFDbENGLDBGQUEyQkEsR0FBWTtBQUV6Qyw2Q0FBNkM7QUFDdEMsTUFBTUcsd0JBQXdCO0lBQ25DLE1BQU1DLGNBQWNDLDBCQUFvQztJQUN4RCxNQUFNRyxxQkFBcUJILFFBQVFDLEdBQUcsQ0FBQ0cseUJBQXlCO0lBRWhFLE9BQU9SLG1FQUFZQSxDQUFXRyxhQUFhSSxvQkFBb0I7UUFDN0RFLE1BQU07WUFDSkMsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtBQUNGLEVBQUM7QUFFRCw0Q0FBNEM7QUFDckMsU0FBU0MsY0FBY0MsUUFBZ0IsRUFBRUMsWUFBb0I7SUFDbEUsTUFBTUMsZ0JBQWdCO1FBQ3BCLFlBQVk7UUFDWixZQUFZO1FBQ1osU0FBUztRQUNULFdBQVc7UUFDWCxTQUFTO1FBQ1QsU0FBUztRQUNULFVBQVU7SUFDWjtJQUVBLE1BQU1DLFlBQVlELGFBQWEsQ0FBQ0YsU0FBdUMsSUFBSTtJQUMzRSxNQUFNSSxnQkFBZ0JGLGFBQWEsQ0FBQ0QsYUFBMkMsSUFBSTtJQUVuRixPQUFPRSxhQUFhQztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLy4vbGliL3N1cGFiYXNlLnRzP2M5OWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL2F1dGgtaGVscGVycy1uZXh0anMnXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5pbXBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC90eXBlcy9kYXRhYmFzZSdcblxuLy8gQ2xpZW50LXNpZGUgU3VwYWJhc2UgY2xpZW50XG5leHBvcnQgY29uc3QgY3JlYXRlQ2xpZW50U3VwYWJhc2UgPSAoKSA9PlxuICBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQ8RGF0YWJhc2U+KClcblxuLy8gU2VydmljZSByb2xlIGNsaWVudCAoZm9yIGFkbWluIG9wZXJhdGlvbnMpXG5leHBvcnQgY29uc3QgY3JlYXRlU2VydmljZVN1cGFiYXNlID0gKCkgPT4ge1xuICBjb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbiAgY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSFcblxuICByZXR1cm4gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VTZXJ2aWNlS2V5LCB7XG4gICAgYXV0aDoge1xuICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICBwZXJzaXN0U2Vzc2lvbjogZmFsc2VcbiAgICB9XG4gIH0pXG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjaGVjayB1c2VyIHBlcm1pc3Npb25zXG5leHBvcnQgZnVuY3Rpb24gaGFzUGVybWlzc2lvbih1c2VyUm9sZTogc3RyaW5nLCByZXF1aXJlZFJvbGU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICBjb25zdCByb2xlSGllcmFyY2h5ID0ge1xuICAgICdwcm92aWRlcic6IDUsXG4gICAgJ3Jlc2VsbGVyJzogNCxcbiAgICAnYWRtaW4nOiAzLFxuICAgICdzdXBwb3J0JzogMixcbiAgICAnc2FsZXMnOiAyLFxuICAgICdzdGFmZic6IDEsXG4gICAgJ2NsaWVudCc6IDBcbiAgfVxuXG4gIGNvbnN0IHVzZXJMZXZlbCA9IHJvbGVIaWVyYXJjaHlbdXNlclJvbGUgYXMga2V5b2YgdHlwZW9mIHJvbGVIaWVyYXJjaHldIHx8IDBcbiAgY29uc3QgcmVxdWlyZWRMZXZlbCA9IHJvbGVIaWVyYXJjaHlbcmVxdWlyZWRSb2xlIGFzIGtleW9mIHR5cGVvZiByb2xlSGllcmFyY2h5XSB8fCAwXG5cbiAgcmV0dXJuIHVzZXJMZXZlbCA+PSByZXF1aXJlZExldmVsXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50IiwiY3JlYXRlQ2xpZW50IiwiY3JlYXRlQ2xpZW50U3VwYWJhc2UiLCJjcmVhdGVTZXJ2aWNlU3VwYWJhc2UiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZVNlcnZpY2VLZXkiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImhhc1Blcm1pc3Npb24iLCJ1c2VyUm9sZSIsInJlcXVpcmVkUm9sZSIsInJvbGVIaWVyYXJjaHkiLCJ1c2VyTGV2ZWwiLCJyZXF1aXJlZExldmVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   generatePassword: () => (/* binding */ generatePassword),\n/* harmony export */   generateSIPUsername: () => (/* binding */ generateSIPUsername),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getRoleColor: () => (/* binding */ getRoleColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction generatePassword(length = 12) {\n    const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\n    let password = \"\";\n    for(let i = 0; i < length; i++){\n        password += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n    return password;\n}\nfunction generateSIPUsername(prefix = \"sip\") {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substring(2, 8);\n    return `${prefix}_${timestamp}_${random}`;\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n    return phoneRegex.test(phone) && phone.replace(/\\D/g, \"\").length >= 10;\n}\nfunction formatPhoneNumber(phone) {\n    const cleaned = phone.replace(/\\D/g, \"\");\n    if (cleaned.length === 10) {\n        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    if (cleaned.length === 11 && cleaned[0] === \"1\") {\n        return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\n    }\n    return phone;\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\nfunction getInitials(firstName, lastName) {\n    const first = firstName?.charAt(0)?.toUpperCase() || \"\";\n    const last = lastName?.charAt(0)?.toUpperCase() || \"\";\n    return first + last || \"??\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"active\":\n            return \"text-green-600 bg-green-100\";\n        case \"suspended\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"inactive\":\n            return \"text-red-600 bg-red-100\";\n        case \"pending\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"open\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"in_progress\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"resolved\":\n            return \"text-green-600 bg-green-100\";\n        case \"closed\":\n            return \"text-gray-600 bg-gray-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getRoleColor(role) {\n    switch(role.toLowerCase()){\n        case \"provider\":\n            return \"text-purple-600 bg-purple-100\";\n        case \"reseller\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"admin\":\n            return \"text-red-600 bg-red-100\";\n        case \"support\":\n            return \"text-green-600 bg-green-100\";\n        case \"sales\":\n            return \"text-orange-600 bg-orange-100\";\n        case \"staff\":\n            return \"text-gray-600 bg-gray-100\";\n        case \"client\":\n            return \"text-indigo-600 bg-indigo-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"807acf88b172\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcGFhcy1wbGF0Zm9ybS8uL2FwcC9nbG9iYWxzLmNzcz8zODI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODA3YWNmODhiMTcyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-layout */ \"(rsc)/./components/dashboard/dashboard-layout.tsx\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\n\n\n\n\nasync function Layout({ children }) {\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createServerComponentClient)({\n        cookies: next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies\n    });\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!session) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.redirect)(\"/auth/signin\");\n    }\n    const profile = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_5__.getUserProfile)(session.user.id);\n    if (!profile) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.redirect)(\"/auth/signin\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_4__.DashboardLayout, {\n        profile: profile,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./components/providers/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"SPaaS Platform - SIP Platform as a Service\",\n    description: \"Multi-tenant SIP/Telecom platform with billing, support, and real-time management\",\n    keywords: [\n        \"SIP\",\n        \"VoIP\",\n        \"Telecom\",\n        \"Platform\",\n        \"Multi-tenant\",\n        \"Billing\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                },\n                                success: {\n                                    duration: 3000,\n                                    iconTheme: {\n                                        primary: \"#4ade80\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    duration: 5000,\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/dashboard/dashboard-layout.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-layout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\dashboard\dashboard-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\dashboard\dashboard-layout.tsx#DashboardLayout`);


/***/ }),

/***/ "(rsc)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\auth-provider.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\test\components\providers\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   getTenantData: () => (/* binding */ getTenantData),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Server-side Supabase client\nconst createServerSupabase = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies\n    });\n// Helper function to get user profile with tenant info\nasync function getUserProfile(userId) {\n    const supabase = createServerSupabase();\n    const { data: user, error } = await supabase.from(\"users\").select(`\n      *,\n      tenant:tenants(*)\n    `).eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error fetching user profile:\", error);\n        return null;\n    }\n    return user;\n}\n// Helper function to check user permissions\nfunction hasPermission(userRole, requiredRole) {\n    const roleHierarchy = {\n        \"provider\": 5,\n        \"reseller\": 4,\n        \"admin\": 3,\n        \"support\": 2,\n        \"sales\": 2,\n        \"staff\": 1,\n        \"client\": 0\n    };\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n// Helper function to get tenant-specific data\nasync function getTenantData(tenantId) {\n    const supabase = createServerSupabase();\n    const { data: tenant, error } = await supabase.from(\"tenants\").select(\"*\").eq(\"id\", tenantId).single();\n    if (error) {\n        console.error(\"Error fetching tenant data:\", error);\n        return null;\n    }\n    return tenant;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/@radix-ui","vendor-chunks/jose","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDocuments%5Caugment-projects%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();