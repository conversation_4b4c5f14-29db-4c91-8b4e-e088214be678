import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { SipAccountsManagement } from '@/components/sip-accounts/sip-accounts-management'
import { getUserProfile } from '@/lib/supabase-server'

export default async function SipAccountsPage() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const profile = await getUserProfile(session.user.id)
  
  if (!profile) {
    redirect('/auth/signin')
  }

  return <SipAccountsManagement profile={profile} />
}
