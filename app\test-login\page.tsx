'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'react-hot-toast'

export default function TestLoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('password123')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleTestLogin = async () => {
    setLoading(true)
    
    try {
      // Simulate demo login
      toast.success('Test login successful! Redirecting...')
      
      // Direct redirect to dashboard
      setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
      
    } catch (error) {
      toast.error('Test login failed')
    } finally {
      setLoading(false)
    }
  }

  const handleDirectDashboard = () => {
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Test Login Page
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Тестовая страница для проверки входа в демо-режиме
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Demo Login Test</CardTitle>
            <CardDescription>
              Проверим, работает ли вход в демо-режиме
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="password123"
              />
            </div>

            <div className="space-y-2">
              <Button 
                onClick={handleTestLogin}
                disabled={loading}
                className="w-full"
              >
                {loading ? 'Testing...' : 'Test Demo Login'}
              </Button>

              <Button 
                onClick={handleDirectDashboard}
                variant="outline"
                className="w-full"
              >
                Go Direct to Dashboard
              </Button>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Инструкции для тестирования:
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>1. Попробуйте "Test Demo Login"</li>
                <li>2. Если не работает, попробуйте "Go Direct to Dashboard"</li>
                <li>3. Проверьте консоль браузера на ошибки (F12)</li>
                <li>4. Убедитесь, что сервер запущен на localhost:3000</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-medium text-gray-800 mb-2">
                Альтернативные способы:
              </h3>
              <div className="space-y-2">
                <Button 
                  onClick={() => router.push('/auth/signin')}
                  variant="ghost"
                  size="sm"
                  className="w-full"
                >
                  Обычная страница входа
                </Button>
                <Button 
                  onClick={() => router.push('/')}
                  variant="ghost"
                  size="sm"
                  className="w-full"
                >
                  Главная страница
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Если ничего не работает, проверьте терминал на ошибки
          </p>
        </div>
      </div>
    </div>
  )
}
