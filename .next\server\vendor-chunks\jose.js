"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose";
exports.ids = ["vendor-chunks/jose"];
exports.modules = {

/***/ "(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length })=>acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    buffers.forEach((buffer)=>{\n        buf.set(buffer, i);\n        i += buffer.length;\n    });\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([\n        0\n    ]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([\n        value >>> 24,\n        value >>> 16,\n        value >>> 8,\n        value & 0xff\n    ], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for(let iter = 0; iter < iterations; iter++){\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sha256\", buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nlet encode;\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nif (buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.isEncoding(\"base64url\")) {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64url\");\n} else {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\").replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\nconst decodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, \"base64\");\nconst encodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\");\n\nconst decode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), \"base64\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst digest = (algorithm, data)=>(0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBQ3BDLE1BQU1DLFNBQVMsQ0FBQ0MsV0FBV0MsT0FBU0gsa0RBQVVBLENBQUNFLFdBQVdFLE1BQU0sQ0FBQ0QsTUFBTUYsTUFBTTtBQUM3RSxpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2pvc2UvZGlzdC9ub2RlL2VzbS9ydW50aW1lL2RpZ2VzdC5qcz9mYzM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUhhc2ggfSBmcm9tICdjcnlwdG8nO1xuY29uc3QgZGlnZXN0ID0gKGFsZ29yaXRobSwgZGF0YSkgPT4gY3JlYXRlSGFzaChhbGdvcml0aG0pLnVwZGF0ZShkYXRhKS5kaWdlc3QoKTtcbmV4cG9ydCBkZWZhdWx0IGRpZ2VzdDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVIYXNoIiwiZGlnZXN0IiwiYWxnb3JpdGhtIiwiZGF0YSIsInVwZGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/util/base64url.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/base64url.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QyxNQUFNQyxTQUFTRCx5REFBZ0IsQ0FBQztBQUNoQyxNQUFNRSxTQUFTRix5REFBZ0IsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2pvc2UvZGlzdC9ub2RlL2VzbS91dGlsL2Jhc2U2NHVybC5qcz85ZWNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGJhc2U2NHVybCBmcm9tICcuLi9ydW50aW1lL2Jhc2U2NHVybC5qcyc7XG5leHBvcnQgY29uc3QgZW5jb2RlID0gYmFzZTY0dXJsLmVuY29kZTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBiYXNlNjR1cmwuZGVjb2RlO1xuIl0sIm5hbWVzIjpbImJhc2U2NHVybCIsImVuY29kZSIsImRlY29kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length })=>acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    buffers.forEach((buffer)=>{\n        buf.set(buffer, i);\n        i += buffer.length;\n    });\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([\n        0\n    ]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([\n        value >>> 24,\n        value >>> 16,\n        value >>> 8,\n        value & 0xff\n    ], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for(let iter = 0; iter < iterations; iter++){\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sha256\", buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9idWZmZXJfdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ25DLE1BQU1DLFVBQVUsSUFBSUMsY0FBYztBQUNsQyxNQUFNQyxVQUFVLElBQUlDLGNBQWM7QUFDekMsTUFBTUMsWUFBWSxLQUFLO0FBQ2hCLFNBQVNDLE9BQU8sR0FBR0MsT0FBTztJQUM3QixNQUFNQyxPQUFPRCxRQUFRRSxNQUFNLENBQUMsQ0FBQ0MsS0FBSyxFQUFFQyxNQUFNLEVBQUUsR0FBS0QsTUFBTUMsUUFBUTtJQUMvRCxNQUFNQyxNQUFNLElBQUlDLFdBQVdMO0lBQzNCLElBQUlNLElBQUk7SUFDUlAsUUFBUVEsT0FBTyxDQUFDLENBQUNDO1FBQ2JKLElBQUlLLEdBQUcsQ0FBQ0QsUUFBUUY7UUFDaEJBLEtBQUtFLE9BQU9MLE1BQU07SUFDdEI7SUFDQSxPQUFPQztBQUNYO0FBQ08sU0FBU00sSUFBSUMsR0FBRyxFQUFFQyxRQUFRO0lBQzdCLE9BQU9kLE9BQU9MLFFBQVFvQixNQUFNLENBQUNGLE1BQU0sSUFBSU4sV0FBVztRQUFDO0tBQUUsR0FBR087QUFDNUQ7QUFDQSxTQUFTRSxjQUFjVixHQUFHLEVBQUVXLEtBQUssRUFBRUMsTUFBTTtJQUNyQyxJQUFJRCxRQUFRLEtBQUtBLFNBQVNsQixXQUFXO1FBQ2pDLE1BQU0sSUFBSW9CLFdBQVcsQ0FBQywwQkFBMEIsRUFBRXBCLFlBQVksRUFBRSxXQUFXLEVBQUVrQixNQUFNLENBQUM7SUFDeEY7SUFDQVgsSUFBSUssR0FBRyxDQUFDO1FBQUNNLFVBQVU7UUFBSUEsVUFBVTtRQUFJQSxVQUFVO1FBQUdBLFFBQVE7S0FBSyxFQUFFQztBQUNyRTtBQUNPLFNBQVNFLFNBQVNILEtBQUs7SUFDMUIsTUFBTUksT0FBT0MsS0FBS0MsS0FBSyxDQUFDTixRQUFRbEI7SUFDaEMsTUFBTXlCLE1BQU1QLFFBQVFsQjtJQUNwQixNQUFNTyxNQUFNLElBQUlDLFdBQVc7SUFDM0JTLGNBQWNWLEtBQUtlLE1BQU07SUFDekJMLGNBQWNWLEtBQUtrQixLQUFLO0lBQ3hCLE9BQU9sQjtBQUNYO0FBQ08sU0FBU21CLFNBQVNSLEtBQUs7SUFDMUIsTUFBTVgsTUFBTSxJQUFJQyxXQUFXO0lBQzNCUyxjQUFjVixLQUFLVztJQUNuQixPQUFPWDtBQUNYO0FBQ08sU0FBU29CLGVBQWVDLEtBQUs7SUFDaEMsT0FBTzNCLE9BQU95QixTQUFTRSxNQUFNdEIsTUFBTSxHQUFHc0I7QUFDMUM7QUFDTyxlQUFlQyxVQUFVQyxNQUFNLEVBQUVDLElBQUksRUFBRWIsS0FBSztJQUMvQyxNQUFNYyxhQUFhVCxLQUFLVSxJQUFJLENBQUMsQ0FBQ0YsUUFBUSxLQUFLO0lBQzNDLE1BQU1HLE1BQU0sSUFBSTFCLFdBQVd3QixhQUFhO0lBQ3hDLElBQUssSUFBSUcsT0FBTyxHQUFHQSxPQUFPSCxZQUFZRyxPQUFRO1FBQzFDLE1BQU01QixNQUFNLElBQUlDLFdBQVcsSUFBSXNCLE9BQU94QixNQUFNLEdBQUdZLE1BQU1aLE1BQU07UUFDM0RDLElBQUlLLEdBQUcsQ0FBQ2MsU0FBU1MsT0FBTztRQUN4QjVCLElBQUlLLEdBQUcsQ0FBQ2tCLFFBQVE7UUFDaEJ2QixJQUFJSyxHQUFHLENBQUNNLE9BQU8sSUFBSVksT0FBT3hCLE1BQU07UUFDaEM0QixJQUFJdEIsR0FBRyxDQUFDLE1BQU1qQiw4REFBTUEsQ0FBQyxVQUFVWSxNQUFNNEIsT0FBTztJQUNoRDtJQUNBLE9BQU9ELElBQUlFLEtBQUssQ0FBQyxHQUFHTCxRQUFRO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3BhYXMtcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL2xpYi9idWZmZXJfdXRpbHMuanM/NmQ1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZGlnZXN0IGZyb20gJy4uL3J1bnRpbWUvZGlnZXN0LmpzJztcbmV4cG9ydCBjb25zdCBlbmNvZGVyID0gbmV3IFRleHRFbmNvZGVyKCk7XG5leHBvcnQgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpO1xuY29uc3QgTUFYX0lOVDMyID0gMiAqKiAzMjtcbmV4cG9ydCBmdW5jdGlvbiBjb25jYXQoLi4uYnVmZmVycykge1xuICAgIGNvbnN0IHNpemUgPSBidWZmZXJzLnJlZHVjZSgoYWNjLCB7IGxlbmd0aCB9KSA9PiBhY2MgKyBsZW5ndGgsIDApO1xuICAgIGNvbnN0IGJ1ZiA9IG5ldyBVaW50OEFycmF5KHNpemUpO1xuICAgIGxldCBpID0gMDtcbiAgICBidWZmZXJzLmZvckVhY2goKGJ1ZmZlcikgPT4ge1xuICAgICAgICBidWYuc2V0KGJ1ZmZlciwgaSk7XG4gICAgICAgIGkgKz0gYnVmZmVyLmxlbmd0aDtcbiAgICB9KTtcbiAgICByZXR1cm4gYnVmO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHAycyhhbGcsIHAyc0lucHV0KSB7XG4gICAgcmV0dXJuIGNvbmNhdChlbmNvZGVyLmVuY29kZShhbGcpLCBuZXcgVWludDhBcnJheShbMF0pLCBwMnNJbnB1dCk7XG59XG5mdW5jdGlvbiB3cml0ZVVJbnQzMkJFKGJ1ZiwgdmFsdWUsIG9mZnNldCkge1xuICAgIGlmICh2YWx1ZSA8IDAgfHwgdmFsdWUgPj0gTUFYX0lOVDMyKSB7XG4gICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKGB2YWx1ZSBtdXN0IGJlID49IDAgYW5kIDw9ICR7TUFYX0lOVDMyIC0gMX0uIFJlY2VpdmVkICR7dmFsdWV9YCk7XG4gICAgfVxuICAgIGJ1Zi5zZXQoW3ZhbHVlID4+PiAyNCwgdmFsdWUgPj4+IDE2LCB2YWx1ZSA+Pj4gOCwgdmFsdWUgJiAweGZmXSwgb2Zmc2V0KTtcbn1cbmV4cG9ydCBmdW5jdGlvbiB1aW50NjRiZSh2YWx1ZSkge1xuICAgIGNvbnN0IGhpZ2ggPSBNYXRoLmZsb29yKHZhbHVlIC8gTUFYX0lOVDMyKTtcbiAgICBjb25zdCBsb3cgPSB2YWx1ZSAlIE1BWF9JTlQzMjtcbiAgICBjb25zdCBidWYgPSBuZXcgVWludDhBcnJheSg4KTtcbiAgICB3cml0ZVVJbnQzMkJFKGJ1ZiwgaGlnaCwgMCk7XG4gICAgd3JpdGVVSW50MzJCRShidWYsIGxvdywgNCk7XG4gICAgcmV0dXJuIGJ1Zjtcbn1cbmV4cG9ydCBmdW5jdGlvbiB1aW50MzJiZSh2YWx1ZSkge1xuICAgIGNvbnN0IGJ1ZiA9IG5ldyBVaW50OEFycmF5KDQpO1xuICAgIHdyaXRlVUludDMyQkUoYnVmLCB2YWx1ZSk7XG4gICAgcmV0dXJuIGJ1Zjtcbn1cbmV4cG9ydCBmdW5jdGlvbiBsZW5ndGhBbmRJbnB1dChpbnB1dCkge1xuICAgIHJldHVybiBjb25jYXQodWludDMyYmUoaW5wdXQubGVuZ3RoKSwgaW5wdXQpO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNvbmNhdEtkZihzZWNyZXQsIGJpdHMsIHZhbHVlKSB7XG4gICAgY29uc3QgaXRlcmF0aW9ucyA9IE1hdGguY2VpbCgoYml0cyA+PiAzKSAvIDMyKTtcbiAgICBjb25zdCByZXMgPSBuZXcgVWludDhBcnJheShpdGVyYXRpb25zICogMzIpO1xuICAgIGZvciAobGV0IGl0ZXIgPSAwOyBpdGVyIDwgaXRlcmF0aW9uczsgaXRlcisrKSB7XG4gICAgICAgIGNvbnN0IGJ1ZiA9IG5ldyBVaW50OEFycmF5KDQgKyBzZWNyZXQubGVuZ3RoICsgdmFsdWUubGVuZ3RoKTtcbiAgICAgICAgYnVmLnNldCh1aW50MzJiZShpdGVyICsgMSkpO1xuICAgICAgICBidWYuc2V0KHNlY3JldCwgNCk7XG4gICAgICAgIGJ1Zi5zZXQodmFsdWUsIDQgKyBzZWNyZXQubGVuZ3RoKTtcbiAgICAgICAgcmVzLnNldChhd2FpdCBkaWdlc3QoJ3NoYTI1NicsIGJ1ZiksIGl0ZXIgKiAzMik7XG4gICAgfVxuICAgIHJldHVybiByZXMuc2xpY2UoMCwgYml0cyA+PiAzKTtcbn1cbiJdLCJuYW1lcyI6WyJkaWdlc3QiLCJlbmNvZGVyIiwiVGV4dEVuY29kZXIiLCJkZWNvZGVyIiwiVGV4dERlY29kZXIiLCJNQVhfSU5UMzIiLCJjb25jYXQiLCJidWZmZXJzIiwic2l6ZSIsInJlZHVjZSIsImFjYyIsImxlbmd0aCIsImJ1ZiIsIlVpbnQ4QXJyYXkiLCJpIiwiZm9yRWFjaCIsImJ1ZmZlciIsInNldCIsInAycyIsImFsZyIsInAyc0lucHV0IiwiZW5jb2RlIiwid3JpdGVVSW50MzJCRSIsInZhbHVlIiwib2Zmc2V0IiwiUmFuZ2VFcnJvciIsInVpbnQ2NGJlIiwiaGlnaCIsIk1hdGgiLCJmbG9vciIsImxvdyIsInVpbnQzMmJlIiwibGVuZ3RoQW5kSW5wdXQiLCJpbnB1dCIsImNvbmNhdEtkZiIsInNlY3JldCIsImJpdHMiLCJpdGVyYXRpb25zIiwiY2VpbCIsInJlcyIsIml0ZXIiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nlet encode;\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nif (buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.isEncoding(\"base64url\")) {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64url\");\n} else {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\").replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\nconst decodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, \"base64\");\nconst encodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\");\n\nconst decode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), \"base64\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst digest = (algorithm, data)=>(0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBQ3BDLE1BQU1DLFNBQVMsQ0FBQ0MsV0FBV0MsT0FBU0gsa0RBQVVBLENBQUNFLFdBQVdFLE1BQU0sQ0FBQ0QsTUFBTUYsTUFBTTtBQUM3RSxpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2pvc2UvZGlzdC9ub2RlL2VzbS9ydW50aW1lL2RpZ2VzdC5qcz9mYzM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUhhc2ggfSBmcm9tICdjcnlwdG8nO1xuY29uc3QgZGlnZXN0ID0gKGFsZ29yaXRobSwgZGF0YSkgPT4gY3JlYXRlSGFzaChhbGdvcml0aG0pLnVwZGF0ZShkYXRhKS5kaWdlc3QoKTtcbmV4cG9ydCBkZWZhdWx0IGRpZ2VzdDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVIYXNoIiwiZGlnZXN0IiwiYWxnb3JpdGhtIiwiZGF0YSIsInVwZGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/base64url.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QyxNQUFNQyxTQUFTRCx5REFBZ0IsQ0FBQztBQUNoQyxNQUFNRSxTQUFTRix5REFBZ0IsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NwYWFzLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2pvc2UvZGlzdC9ub2RlL2VzbS91dGlsL2Jhc2U2NHVybC5qcz85ZWNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGJhc2U2NHVybCBmcm9tICcuLi9ydW50aW1lL2Jhc2U2NHVybC5qcyc7XG5leHBvcnQgY29uc3QgZW5jb2RlID0gYmFzZTY0dXJsLmVuY29kZTtcbmV4cG9ydCBjb25zdCBkZWNvZGUgPSBiYXNlNjR1cmwuZGVjb2RlO1xuIl0sIm5hbWVzIjpbImJhc2U2NHVybCIsImVuY29kZSIsImRlY29kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ })

};
;