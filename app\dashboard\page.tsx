import { redirect } from 'next/navigation'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { DashboardOverview } from '@/components/dashboard/dashboard-overview'
import { getUserProfile } from '@/lib/supabase-server'

export default async function DashboardPage() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const profile = await getUserProfile(session.user.id)
  
  if (!profile) {
    redirect('/auth/signin')
  }

  return <DashboardOverview profile={profile} />
}
