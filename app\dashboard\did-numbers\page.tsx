'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { DidNumbersManagement } from '@/components/did-numbers/did-numbers-management'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'

export default function DidNumbersPage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()

  // Check if we're in demo mode
  const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL ||
                    process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')

  useEffect(() => {
    // Only redirect if not in demo mode and not authenticated
    if (!isDemoMode && !loading && !user) {
      router.push('/auth/signin')
    }
  }, [user, loading, router, isDemoMode])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // In demo mode, create a demo profile if none exists
  const demoProfile = profile || {
    id: 'demo-user-id',
    tenant_id: 'demo-tenant-id',
    email: '<EMAIL>',
    first_name: 'Demo',
    last_name: 'User',
    role: 'admin',
    permissions: {},
    phone: null,
    is_active: true,
    tenant: {
      id: 'demo-tenant-id',
      name: 'Demo Company',
      domain: 'demo.local',
      logo_url: null,
      primary_color: '#3B82F6',
      secondary_color: '#1E40AF',
      status: 'active'
    }
  }

  // Don't render if not authenticated and not in demo mode
  if (!isDemoMode && !user) {
    return null
  }

  // Check if user has permission to view DID numbers (in demo mode, allow all)
  if (!isDemoMode && !['provider', 'reseller', 'admin', 'support'].includes(demoProfile.role)) {
    router.push('/dashboard')
    return null
  }

  return (
    <DashboardLayout profile={demoProfile}>
      <DidNumbersManagement profile={demoProfile} />
    </DashboardLayout>
  )
}
