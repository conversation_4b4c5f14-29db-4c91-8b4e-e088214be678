'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { createClientSupabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { Loader2, AlertTriangle } from 'lucide-react'

interface Client {
  id: string
  name: string
  email: string | null
  company: string | null
}

interface DeleteClientDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  client: Client | null
  onDeleted: () => void
}

export function DeleteClientDialog({ open, onOpenChange, client, onDeleted }: DeleteClientDialogProps) {
  const [loading, setLoading] = useState(false)
  const supabase = createClientSupabase()

  const handleDelete = async () => {
    if (!client) return

    setLoading(true)

    try {
      // First check if client has any associated records
      const [sipAccountsResult, didNumbersResult, ticketsResult, invoicesResult] = await Promise.all([
        supabase.from('sip_accounts').select('id').eq('client_id', client.id).limit(1),
        supabase.from('did_numbers').select('id').eq('client_id', client.id).limit(1),
        supabase.from('tickets').select('id').eq('client_id', client.id).limit(1),
        supabase.from('invoices').select('id').eq('client_id', client.id).limit(1)
      ])

      const hasAssociatedRecords = 
        (sipAccountsResult.data && sipAccountsResult.data.length > 0) ||
        (didNumbersResult.data && didNumbersResult.data.length > 0) ||
        (ticketsResult.data && ticketsResult.data.length > 0) ||
        (invoicesResult.data && invoicesResult.data.length > 0)

      if (hasAssociatedRecords) {
        toast.error('Cannot delete client with associated SIP accounts, DID numbers, tickets, or invoices. Please remove or reassign these first.')
        return
      }

      // Delete the client
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', client.id)

      if (error) throw error

      // Log the deletion
      await supabase.from('logs').insert({
        action: 'client_deleted',
        resource_type: 'client',
        resource_id: client.id,
        details: {
          client_name: client.name,
          client_email: client.email
        }
      })

      toast.success('Client deleted successfully!')
      onDeleted()
    } catch (error: any) {
      console.error('Error deleting client:', error)
      toast.error(error.message || 'Failed to delete client')
    } finally {
      setLoading(false)
    }
  }

  if (!client) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span>Delete Client</span>
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the client and remove all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-900 mb-2">You are about to delete:</h4>
            <div className="space-y-1 text-sm text-red-800">
              <p><strong>Name:</strong> {client.name}</p>
              {client.email && <p><strong>Email:</strong> {client.email}</p>}
              {client.company && <p><strong>Company:</strong> {client.company}</p>}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Client'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
