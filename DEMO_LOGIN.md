# 🎮 Демо-режим - Как войти в систему

## ✅ Исправлена ошибка "Failed to fetch"

Мы исправили ошибку подключения к Supabase! Теперь приложение работает в **демо-режиме** с тестовыми данными.

## 🔐 Как войти как администратор

### Шаг 1: Откройте страницу входа
Перейдите на: `http://localhost:3000/auth/signin`

### Шаг 2: Введите любые данные
В демо-режиме можно использовать **любые** email и пароль:

```
Email: <EMAIL>
Password: password123
```

Или любые другие данные:
```
Email: <EMAIL>  
Password: 123456
```

### Шаг 3: Войдите в систему
Нажмите **"Sign in"** - вы автоматически войдете как администратор!

## 🎯 Что увидите после входа

### 📊 Dashboard
- Статистика: 12 клиентов, 28 SIP аккаунтов
- Месячная выручка: $2,450
- Последние действия
- Быстрые действия

### 👥 Clients (Клиенты)
- **Acme Corporation** - активный клиент
- **TechStart Inc** - с отрицательным балансом
- **Global Solutions** - приостановленный

### 📞 SIP Accounts
- **acme_user1** - активный аккаунт G.711
- **techstart_admin** - с шифрованием TLS
- **global_support** - приостановленный

### 🎛️ Call Flows
- **Customer Support IVR** - полный сценарий поддержки
- **Sales Department** - прямая линия продаж
- **After Hours Message** - сообщение вне рабочих часов

## 👁️ Live Preview функции

### Call Flow Preview
1. Перейдите в **Call Flows**
2. Нажмите **👁️ Preview** на любом сценарии
3. Нажмите **▶️ Test Flow** для симуляции
4. Наблюдайте пошаговое выполнение

### SIP Config Preview
1. Перейдите в **SIP Accounts**
2. Нажмите **👁️ Preview** на любом аккаунте
3. Просмотрите конфигурацию
4. Скопируйте настройки

## 🎨 Что попробовать

### 1. Создание клиента
- Нажмите **"Add Client"**
- Заполните форму
- Сохраните (данные не сохранятся, но форма работает)

### 2. Создание SIP аккаунта
- Нажмите **"Add SIP Account"**
- Выберите клиента
- Посмотрите автогенерацию паролей
- Настройте кодеки

### 3. Call Flow Builder
- Нажмите **"Create Call Flow"**
- Добавьте шаги
- Настройте параметры
- Протестируйте с Live Preview

### 4. Навигация
- Попробуйте все разделы меню
- Используйте поиск и фильтры
- Проверьте responsive дизайн

## 🔔 Демо-режим индикаторы

### Синий баннер
Вверху страницы вы увидите:
```
🎮 Demo Mode - This is a demonstration. Data is not persistent.
```

### Toast уведомления
При входе/выходе:
```
✅ Successfully signed in! (Demo Mode)
✅ Successfully signed out! (Demo Mode)
```

## 📱 Тестирование на устройствах

### Desktop
- Полная функциональность
- Все панели видны
- Live Preview работает

### Mobile
- Мобильное меню (☰)
- Адаптивные карточки
- Touch-friendly интерфейс

### Tablet
- Средний размер экрана
- Частично скрытые панели
- Оптимизированная навигация

## 🚀 Следующий шаг: Настройка Supabase

Когда будете готовы к реальной базе данных:

### 1. Создайте проект Supabase
```bash
# Перейдите на https://supabase.com
# Создайте новый проект
```

### 2. Обновите .env.local
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Примените миграции
```bash
supabase db push
```

### 4. Создайте администратора
```sql
-- В Supabase SQL Editor
INSERT INTO tenants (name, domain, status) 
VALUES ('Your Company', 'yourcompany.com', 'active');

-- Зарегистрируйтесь через /auth/signup
-- Затем обновите роль на admin
```

## 🎉 Готово!

Теперь вы можете:
- ✅ Войти в систему без ошибок
- ✅ Изучить все функции
- ✅ Протестировать Live Preview
- ✅ Понять архитектуру платформы

**Наслаждайтесь демо-режимом!** 🚀

---

**Вопросы?** Проверьте `SETUP_GUIDE.md` для полной настройки.
